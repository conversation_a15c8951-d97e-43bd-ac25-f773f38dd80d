'use client';

import { useState, useCallback, useMemo, useEffect } from 'react';
import { useQueryClient, skipToken } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useLocale } from 'next-intl';
import {
  useGetRichtexttabsConfigsEntityTypeLanguageCode,
  useGetRichtexttabsConfigsEntityTypeLanguageCodeAll,
  useGetRichtexttabsTabsEntityTypeEntityIdLanguageCode,
  usePostRichtexttabsContent,
  usePostRichtexttabsContentBatch,
  usePostRichtexttabsConfigs,
  usePutRichtexttabsConfigsId,
  useDeleteRichtexttabsConfigsId,
  usePostRichtexttabsConfigsReorder,
  type PostRichtexttabsConfigsRequestBody,
} from '@/api/generated/ayafeedComponents';

export type EntityType = 'event' | 'venue';
export type LanguageCode = 'en' | 'zh' | 'ja';

/**
 * 解析详细的错误信息
 */
function getDetailedErrorMessage(error: any, fallback: string): string {
  try {
    console.log('解析错误对象:', error);

    // 检查 payload 中的详细字段错误信息
    if (error?.payload?.detail?.fieldErrors?.errors) {
      return error.payload.detail.fieldErrors.errors;
    }

    // 检查 data 中的详细字段错误信息 (直接从响应体解析)
    if (error?.data?.detail?.fieldErrors?.errors) {
      return error.data.detail.fieldErrors.errors;
    }

    // 检查 payload 中的业务错误信息
    if (error?.payload?.message) {
      return error.payload.message;
    }

    // 检查 data 中的业务错误信息
    if (error?.data?.message) {
      return error.data.message;
    }

    // 检查直接的错误消息
    if (error?.message && typeof error.message === 'string') {
      // 如果消息包含具体信息，直接返回
      if (error.message !== fallback && !error.message.includes('创建配置失败')) {
        return error.message;
      }
    }

    // 检查字符串错误
    if (typeof error === 'string') {
      return error;
    }

    // 检查是否有错误码对应的消息
    if (error?.payload?.code || error?.data?.code) {
      const code = error.payload?.code || error.data?.code;
      const message = error.payload?.message || error.data?.message || fallback;
      return `${message} (错误码: ${code})`;
    }

    return fallback;
  } catch (e) {
    console.error('解析错误信息失败:', e);
    return fallback;
  }
}

export interface TabConfig {
  id: string;
  entity_type: "event" | "venue";
  language_code: "en" | "zh" | "ja";
  key: string;
  label: string;
  placeholder?: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
  is_preset: boolean;
  deleted_at?: string;
  deleted_by?: string;
  created_at: string;
  updated_at: string;
}

export interface TabContent {
  config: TabConfig;
  content?: string;
}

export interface UseRichTextTabsOptions {
  entityType: EntityType;
  entityId: string;
  languageCode?: LanguageCode;
  autoSave?: boolean;
  autoSaveDelay?: number;
  isAdminMode?: boolean; // 是否为管理模式，获取所有配置（包括已删除的）
}

export interface UseRichTextTabsReturn {
  // 数据状态
  configs: TabConfig[];
  tabs: TabContent[];
  isLoading: boolean;
  isConfigsLoading: boolean;
  isTabsLoading: boolean;
  error: any;

  // 当前状态
  currentLanguage: LanguageCode;
  activeTabKey: string | null;

  // 内容操作
  getTabContent: (tabKey: string) => string;
  updateTabContent: (tabKey: string, content: string) => void;

  // 保存操作
  saveTab: (tabKey: string) => Promise<void>;
  saveAllTabs: () => Promise<void>;
  isSaving: boolean;

  // 语言切换
  switchLanguage: (language: LanguageCode) => void;

  // 标签页操作
  setActiveTab: (tabKey: string) => void;

  // 状态检查
  hasUnsavedChanges: boolean;
  getUnsavedTabs: () => string[];

  // 配置管理操作
  createConfig: (config: Omit<TabConfig, 'id' | 'key' | 'created_at' | 'updated_at'>) => Promise<void>;
  updateConfig: (id: string, updates: Partial<Pick<TabConfig, 'label' | 'placeholder' | 'icon' | 'sort_order' | 'is_active'>>) => Promise<void>;
  deleteConfig: (id: string) => Promise<void>;
  reorderConfigs: (configIds: string[]) => Promise<void>;
  isConfigMutating: boolean;
}

export function useRichTextTabs({
  entityType,
  entityId,
  languageCode,
  autoSave = false,
  autoSaveDelay = 2000,
  isAdminMode = false,
}: UseRichTextTabsOptions): UseRichTextTabsReturn {
  const locale = useLocale() as LanguageCode;
  const currentLanguage = languageCode || locale;
  const queryClient = useQueryClient();
  
  // 本地状态
  const [activeTabKey, setActiveTabKey] = useState<string | null>(null);
  const [localContent, setLocalContent] = useState<Record<string, string>>({});
  const [originalContent, setOriginalContent] = useState<Record<string, string>>({});
  
  // 获取标签页配置 - 普通模式
  const {
    data: configsDataNormal,
    isLoading: isConfigsLoadingNormal,
    error: configsErrorNormal,
  } = useGetRichtexttabsConfigsEntityTypeLanguageCode(
    isAdminMode ? skipToken : {
      pathParams: {
        entityType,
        languageCode: currentLanguage,
      },
    },
    {
      staleTime: 5 * 60 * 1000, // 5分钟缓存
    }
  );

  // 获取标签页配置 - 管理模式
  const {
    data: configsDataAdmin,
    isLoading: isConfigsLoadingAdmin,
    error: configsErrorAdmin,
  } = useGetRichtexttabsConfigsEntityTypeLanguageCodeAll(
    !isAdminMode ? skipToken : {
      pathParams: {
        entityType,
        languageCode: currentLanguage,
      },
    },
    {
      staleTime: 5 * 60 * 1000, // 5分钟缓存
    }
  );

  // 选择正确的数据
  const configsData = isAdminMode ? configsDataAdmin : configsDataNormal;
  const isConfigsLoading = isAdminMode ? isConfigsLoadingAdmin : isConfigsLoadingNormal;
  const configsError = isAdminMode ? configsErrorAdmin : configsErrorNormal;

  // 获取标签页内容
  const {
    data: tabsData,
    isLoading: isTabsLoading,
    error: tabsError,
  } = useGetRichtexttabsTabsEntityTypeEntityIdLanguageCode(
    {
      pathParams: {
        entityType,
        entityId,
        languageCode: currentLanguage,
      },
    },
    {
      staleTime: 2 * 60 * 1000, // 2分钟缓存
      enabled: !!entityId,
    }
  );

  // 使用 useEffect 来处理数据变化
  useEffect(() => {
    let actualTabs: any[] = [];

    // 检查数据结构，与 tabs useMemo 保持一致
    if (tabsData) {
      if ((tabsData as any).data && (tabsData as any).data.tabs) {
        actualTabs = (tabsData as any).data.tabs;
      } else if (tabsData.tabs) {
        actualTabs = tabsData.tabs;
      }
    }

    if (actualTabs.length > 0) {
      // 当数据加载成功时，初始化本地内容
      const contentMap: Record<string, string> = {};
      actualTabs.forEach((tab) => {
        if (tab.content) {
          contentMap[tab.config.key] = tab.content.content || '';
        }
      });

      setLocalContent(contentMap);
      setOriginalContent(contentMap);

      // 设置默认活跃标签页
      if (!activeTabKey && actualTabs.length > 0) {
        setActiveTabKey(actualTabs[0].config.key);
      }
    }
  }, [tabsData, activeTabKey]);
  
  // 保存操作的 mutations
  const saveContentMutation = usePostRichtexttabsContent({
    onSuccess: () => {
      toast.success('内容保存成功');
      // 刷新标签页内容数据
      queryClient.invalidateQueries({
        queryKey: ["rich-text-tabs", "tabs", entityType, entityId, currentLanguage],
      });
      // 重置本地状态为服务器状态
      setOriginalContent({ ...localContent });
    },
    onError: (error) => {
      console.error('保存失败:', error);
      toast.error('保存失败，请重试');
    },
  });

  const saveBatchMutation = usePostRichtexttabsContentBatch({
    onSuccess: () => {
      toast.success('批量保存成功');
      // 刷新标签页内容数据
      queryClient.invalidateQueries({
        queryKey: ["rich-text-tabs", "tabs", entityType, entityId, currentLanguage],
      });
      // 重置本地状态为服务器状态
      setOriginalContent({ ...localContent });
    },
    onError: (error) => {
      console.error('批量保存失败:', error);
      toast.error('批量保存失败，请重试');
    },
  });

  // 配置管理 mutations
  const createConfigMutation = usePostRichtexttabsConfigs({
    onSuccess: (newConfig) => {
      toast.success('标签页创建成功');

      // 立即更新管理模式的配置缓存
      // 查询键格式：["rich-text-tabs", "configs", entityType, languageCode, "all"]
      queryClient.setQueryData(
        ["rich-text-tabs", "configs", entityType, currentLanguage, "all"],
        (oldData: any) => {
          if (Array.isArray(oldData)) {
            return [...oldData, newConfig];
          }
          if (oldData?.data && Array.isArray(oldData.data)) {
            return {
              ...oldData,
              data: [...oldData.data, newConfig]
            };
          }
          return oldData;
        }
      );

      // 失效相关查询以确保数据一致性
      queryClient.invalidateQueries({
        queryKey: ["rich-text-tabs", "configs", entityType, currentLanguage]
      });
      queryClient.invalidateQueries({
        queryKey: ["rich-text-tabs", "configs", entityType, currentLanguage, "all"]
      });
      queryClient.invalidateQueries({
        queryKey: ["rich-text-tabs", "tabs", entityType, entityId, currentLanguage]
      });
    },
    onError: (error) => {
      console.error('创建标签页失败:', error);

      // 解析错误信息
      const errorMessage = getDetailedErrorMessage(error, '创建标签页失败');
      toast.error(errorMessage);
    },
  });

  const updateConfigMutation = usePutRichtexttabsConfigsId({
    onSuccess: (updatedConfig) => {
      toast.success('标签页更新成功');

      // 立即更新缓存数据
      // 查询键格式：["rich-text-tabs", "configs", entityType, languageCode, "all"]
      queryClient.setQueryData(
        ["rich-text-tabs", "configs", entityType, currentLanguage, "all"],
        (oldData: any) => {
          const updateItem = (item: any) => {
            if (item.id === updatedConfig.id) {
              return updatedConfig;
            }
            return item;
          };

          if (Array.isArray(oldData)) {
            return oldData.map(updateItem);
          }
          if (oldData?.data && Array.isArray(oldData.data)) {
            return {
              ...oldData,
              data: oldData.data.map(updateItem)
            };
          }
          return oldData;
        }
      );

      // 失效相关查询以确保数据一致性
      queryClient.invalidateQueries({
        queryKey: ["rich-text-tabs", "configs", entityType, currentLanguage]
      });
      queryClient.invalidateQueries({
        queryKey: ["rich-text-tabs", "tabs", entityType, entityId, currentLanguage]
      });
    },
    onError: (error) => {
      console.error('更新标签页失败:', error);
      const errorMessage = getDetailedErrorMessage(error, '更新标签页失败');
      toast.error(errorMessage);
    },
  });

  const deleteConfigMutation = useDeleteRichtexttabsConfigsId({
    onSuccess: (_, variables) => {
      toast.success('标签页删除成功');

      // 立即从缓存中移除已删除的配置
      // 查询键格式：["rich-text-tabs", "configs", entityType, languageCode, "all"]
      queryClient.setQueryData(
        ["rich-text-tabs", "configs", entityType, currentLanguage, "all"],
        (oldData: any) => {
          const filterItem = (item: any) => item.id !== variables.pathParams.id;

          if (Array.isArray(oldData)) {
            return oldData.filter(filterItem);
          }
          if (oldData?.data && Array.isArray(oldData.data)) {
            return {
              ...oldData,
              data: oldData.data.filter(filterItem)
            };
          }
          return oldData;
        }
      );

      // 失效相关查询以确保数据一致性
      queryClient.invalidateQueries({
        queryKey: ["rich-text-tabs", "configs", entityType, currentLanguage]
      });
      queryClient.invalidateQueries({
        queryKey: ["rich-text-tabs", "tabs", entityType, entityId, currentLanguage]
      });
    },
    onError: (error) => {
      console.error('删除标签页失败:', error);
      const errorMessage = getDetailedErrorMessage(error, '删除标签页失败');
      toast.error(errorMessage);
    },
  });

  const reorderConfigMutation = usePostRichtexttabsConfigsReorder({
    onSuccess: () => {
      toast.success('标签页排序成功');
      // 失效配置查询
      queryClient.invalidateQueries({
        queryKey: ["rich-text-tabs", "configs", entityType, currentLanguage]
      });
      queryClient.invalidateQueries({
        queryKey: ["rich-text-tabs", "configs", entityType, currentLanguage, "all"]
      });
      // 失效标签页内容查询
      queryClient.invalidateQueries({
        queryKey: ["rich-text-tabs", "tabs", entityType, entityId, currentLanguage]
      });
    },
    onError: (error) => {
      console.error('排序失败:', error);
      const errorMessage = getDetailedErrorMessage(error, '排序失败');
      toast.error(errorMessage);
    },
  });

  // 处理数据
  const configs = useMemo(() => {
    // API 返回的数据结构是 { code, message, data }，实际配置在 data 字段中
    if (configsData && typeof configsData === 'object' && 'data' in configsData) {
      return Array.isArray((configsData as any).data) ? (configsData as any).data : [];
    }

    return Array.isArray(configsData) ? configsData : [];
  }, [configsData]);

  const tabs = useMemo(() => {
    // 检查 tabsData 的结构
    if (!tabsData) return [];

    // 如果 tabsData 有 data 字段，使用 data.tabs
    if ((tabsData as any).data && (tabsData as any).data.tabs) {
      return (tabsData as any).data.tabs.map((tab: any) => ({
        config: tab.config,
        content: tab.content?.content || '',
      }));
    }

    // 否则直接使用 tabsData.tabs
    if (tabsData.tabs) {
      return tabsData.tabs.map((tab) => ({
        config: tab.config,
        content: tab.content?.content || '',
      }));
    }

    return [];
  }, [tabsData]);
  
  // 获取标签页内容
  const getTabContent = useCallback((tabKey: string): string => {
    return localContent[tabKey] || '';
  }, [localContent]);
  
  // 更新标签页内容
  const updateTabContent = useCallback((tabKey: string, content: string) => {
    setLocalContent(prev => ({
      ...prev,
      [tabKey]: content,
    }));
  }, []);
  
  // 保存单个标签页
  const saveTab = useCallback(async (tabKey: string) => {
    const content = localContent[tabKey] || '';

    await saveContentMutation.mutateAsync({
      body: {
        entity_type: entityType,
        entity_id: entityId,
        language_code: currentLanguage,
        content_type: tabKey,
        content,
      },
    });

    // 更新原始内容
    setOriginalContent(prev => ({
      ...prev,
      [tabKey]: content,
    }));
  }, [localContent, entityType, entityId, currentLanguage, saveContentMutation]);
  
  // 批量保存所有标签页
  const saveAllTabs = useCallback(async () => {
    const contents: Record<string, string> = {};

    // 只保存有变更的内容
    Object.keys(localContent).forEach(tabKey => {
      if (localContent[tabKey] !== originalContent[tabKey]) {
        contents[tabKey] = localContent[tabKey];
      }
    });

    if (Object.keys(contents).length === 0) {
      toast.info('没有需要保存的更改');
      return;
    }

    await saveBatchMutation.mutateAsync({
      body: {
        entity_type: entityType,
        entity_id: entityId,
        language_code: currentLanguage,
        contents,
      },
    });

    // 更新原始内容
    setOriginalContent(prev => ({
      ...prev,
      ...contents,
    }));
  }, [localContent, originalContent, entityType, entityId, currentLanguage, saveBatchMutation]);
  
  // 语言切换
  const switchLanguage = useCallback((language: LanguageCode) => {
    // 这里可以触发重新获取数据，但由于我们使用的是 currentLanguage，
    // 实际上需要父组件重新渲染来改变 languageCode
    console.log('切换语言到:', language);
  }, []);
  
  // 设置活跃标签页
  const setActiveTab = useCallback((tabKey: string) => {
    setActiveTabKey(tabKey);
  }, []);
  
  // 检查是否有未保存的更改
  const hasUnsavedChanges = useMemo(() => {
    return Object.keys(localContent).some(tabKey => 
      localContent[tabKey] !== originalContent[tabKey]
    );
  }, [localContent, originalContent]);
  
  // 获取有未保存更改的标签页
  const getUnsavedTabs = useCallback(() => {
    return Object.keys(localContent).filter(tabKey => 
      localContent[tabKey] !== originalContent[tabKey]
    );
  }, [localContent, originalContent]);
  
  return {
    // 数据状态
    configs,
    tabs,
    isLoading: isConfigsLoading || isTabsLoading,
    isConfigsLoading,
    isTabsLoading,
    error: configsError || tabsError,
    
    // 当前状态
    currentLanguage,
    activeTabKey,
    
    // 内容操作
    getTabContent,
    updateTabContent,
    
    // 保存操作
    saveTab,
    saveAllTabs,
    isSaving: saveContentMutation.isPending || saveBatchMutation.isPending,
    
    // 语言切换
    switchLanguage,
    
    // 标签页操作
    setActiveTab,
    
    // 状态检查
    hasUnsavedChanges,
    getUnsavedTabs,

    // 配置管理操作
    createConfig: async (config: Omit<TabConfig, 'id' | 'key' | 'created_at' | 'updated_at'>) => {
      console.log('原始配置数据:', config);

      // 验证必填字段
      if (!config.entity_type || !config.language_code || !config.label?.trim()) {
        throw new Error('缺少必填字段: entity_type, language_code, label');
      }

      // 构建请求体，包含 entity_id 以实现配置隔离
      const requestBody: PostRichtexttabsConfigsRequestBody = {
        entity_type: config.entity_type,
        entity_id: entityId, // 使用当前实体 ID 实现配置隔离
        language_code: config.language_code,
        label: config.label.trim(),
      };

      // 只添加有值的可选字段
      if (config.placeholder && typeof config.placeholder === 'string' && config.placeholder.trim()) {
        requestBody.placeholder = config.placeholder.trim();
      }

      if (config.icon && typeof config.icon === 'string' && config.icon.trim()) {
        requestBody.icon = config.icon.trim();
      }

      if (typeof config.sort_order === 'number' && config.sort_order >= 0) {
        requestBody.sort_order = config.sort_order;
      }

      // 只有明确设置了布尔值才发送
      if (typeof config.is_active === 'boolean') {
        requestBody.is_active = config.is_active;
      }

      if (typeof config.is_preset === 'boolean') {
        requestBody.is_preset = config.is_preset;
      }

      console.log('创建配置请求数据:', requestBody);
      console.log('请求数据类型检查:', {
        entity_type: typeof requestBody.entity_type,
        language_code: typeof requestBody.language_code,
        label: typeof requestBody.label,
        placeholder: typeof requestBody.placeholder,
        icon: typeof requestBody.icon,
        sort_order: typeof requestBody.sort_order,
        is_active: typeof requestBody.is_active,
        is_preset: typeof requestBody.is_preset,
      });

      try {
        await createConfigMutation.mutateAsync({
          body: requestBody,
        });
      } catch (error) {
        console.error('创建配置 API 调用失败:', error);
        throw error;
      }
    },
    updateConfig: async (id: string, updates: Partial<Pick<TabConfig, 'label' | 'placeholder' | 'icon' | 'sort_order' | 'is_active'>>) => {
      // 确保布尔字段的类型正确
      const sanitizedUpdates = {
        ...updates,
        ...(updates.is_active !== undefined && { is_active: Boolean(updates.is_active) }),
      };

      await updateConfigMutation.mutateAsync({
        pathParams: { id },
        body: sanitizedUpdates,
      });
    },
    deleteConfig: async (id: string) => {
      await deleteConfigMutation.mutateAsync({
        pathParams: { id },
        body: {
          deleted_by: 'admin', // TODO: 从用户上下文获取
        },
      });
    },
    reorderConfigs: async (configIds: string[]) => {
      const orders = configIds.map((id, index) => ({
        id,
        sort_order: index,
      }));

      await reorderConfigMutation.mutateAsync({
        body: {
          entity_type: entityType,
          language_code: currentLanguage,
          orders,
        },
      });
    },
    isConfigMutating: createConfigMutation.isPending || updateConfigMutation.isPending || deleteConfigMutation.isPending || reorderConfigMutation.isPending,
  };
}
