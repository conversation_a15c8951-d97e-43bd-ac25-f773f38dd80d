/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/auth/sign-up/email": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Register a new user with email */
        post: operations["post_auth_signup_email"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/sign-in/email": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Sign in with email and password */
        post: operations["post_auth_signin_email"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/sign-in/username": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Sign in with username and password */
        post: operations["post_auth_signin_username"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/sign-out": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Sign out current user */
        post: operations["post_auth_signout"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/get-session": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get current user session */
        get: operations["get_auth_getsession"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/events": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 展会列表（公开） */
        get: operations["get_events"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/events/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 展会详情 */
        get: operations["get_events_id"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/events/{id}/circles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 展会社团列表 */
        get: operations["get_events_id_circles"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/events/{id}/appearances": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 展会参展记录 */
        get: operations["get_events_id_appearances"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/venues": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 场馆列表（公开） */
        get: operations["get_venues"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/venues/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 场馆详情 */
        get: operations["get_venues_id"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/circles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 社团列表（公开） */
        get: operations["get_circles"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/circles/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 社团详情（按 ID） */
        get: operations["get_circles_id"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/circles/{id}/appearances": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 社团参展历史 */
        get: operations["get_circles_id_appearances"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/circles/{circleId}/bookmark": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 切换收藏状态 */
        post: operations["post_circles_circleId_bookmark"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/circles/{circleId}/bookmark/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 检查收藏状态 */
        get: operations["get_circles_circleId_bookmark_status"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/user/bookmarks": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取用户收藏列表 */
        get: operations["get_user_bookmarks"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/user/bookmarks/stats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取收藏统计
         * @description 获取用户收藏统计信息，可选择包含收藏的社团ID列表用于前端批量状态检查优化
         */
        get: operations["get_user_bookmarks_stats"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/user/bookmarks/batch": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 批量操作收藏 */
        post: operations["post_user_bookmarks_batch"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/artists": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 作者列表 */
        get: operations["get_artists"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/artists/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 作者详情 */
        get: operations["get_artists_id"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/appearances": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 参展记录查询
         * @deprecated
         */
        get: operations["get_appearances"];
        put?: never;
        /** 创建参展记录 */
        post: operations["post_appearances"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/appearances/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 参展记录详情 */
        get: operations["get_appearances_id"];
        put?: never;
        post?: never;
        /** 删除参展记录 */
        delete: operations["delete_appearances_id"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/search": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 搜索内容 */
        get: operations["get_search"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/feed": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取 Feed 流 */
        get: operations["get_feed"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/images/batch": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 批量查询图片 */
        get: operations["get_images_batch"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/images/{id}/file": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 直接访问图片文件 */
        get: operations["get_images_id_file"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/images/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取单个图片信息 */
        get: operations["get_images_id"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/images/{category}/{resourceId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取资源的图片列表 */
        get: operations["get_images_category_resourceId"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text/{entityType}/{entityId}/content": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取实体的所有富文本内容
         * @description 获取指定实体（事件、场馆、社团）的所有富文本内容
         */
        get: operations["get_richtext_entityType_entityId_content"];
        /**
         * 批量更新内容
         * @description 批量更新指定实体的所有富文本内容
         */
        put: operations["put_richtext_entityType_entityId_content"];
        /**
         * 创建或更新单个内容
         * @description 创建或更新指定实体的单个富文本内容
         */
        post: operations["post_richtext_entityType_entityId_content"];
        /**
         * 删除实体的所有内容
         * @description 删除指定实体的所有富文本内容
         */
        delete: operations["delete_richtext_entityType_entityId_content"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text/{entityType}/{entityId}/content/{contentType}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取实体的特定类型内容
         * @description 获取指定实体的特定类型富文本内容
         */
        get: operations["get_richtext_entityType_entityId_content_contentType"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text/api/upload/images": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 富文本编辑器图片上传
         * @description 为富文本编辑器提供图片上传功能
         */
        post: operations["post_richtext_api_upload_images"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/configs/{entityType}/{languageCode}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取实体的活跃标签页配置
         * @description 获取指定实体类型和语言的所有活跃标签页配置
         */
        get: operations["get_richtexttabs_configs_entityType_languageCode"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/configs/{entityType}/{entityId}/{languageCode}/all": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取实体的所有标签页配置
         * @description 获取指定实体实例和语言的所有标签页配置，包括已删除的（管理界面用）
         */
        get: operations["get_richtexttabs_configs_entityType_entityId_languageCode_all"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/configs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 创建新的标签页配置
         * @description 为指定实体类型和语言创建新的标签页配置。Key 由后端自动生成
         */
        post: operations["post_richtexttabs_configs"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/configs/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * 更新标签页配置
         * @description 更新指定的标签页配置
         */
        put: operations["put_richtexttabs_configs_id"];
        post?: never;
        /**
         * 删除标签页配置
         * @description 软删除指定的标签页配置（预设配置不能删除）
         */
        delete: operations["delete_richtexttabs_configs_id"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/configs/{id}/restore": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 恢复已删除的配置
         * @description 恢复指定的已删除标签页配置
         */
        post: operations["post_richtexttabs_configs_id_restore"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/configs/reorder": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 重新排序标签页配置
         * @description 批量更新标签页配置的排序
         */
        post: operations["post_richtexttabs_configs_reorder"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/configs/batch-status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 批量更新配置状态
         * @description 批量启用或禁用标签页配置
         */
        post: operations["post_richtexttabs_configs_batchstatus"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/configs/suggest-keys": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取 Key 建议
         * @description 根据标签名称和实体类型获取推荐的 key 值
         */
        get: operations["get_richtexttabs_configs_suggestkeys"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/tabs/{entityType}/{entityId}/{languageCode}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取实体的完整标签页数据
         * @description 获取指定实体的标签页配置和内容数据
         */
        get: operations["get_richtexttabs_tabs_entityType_entityId_languageCode"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/content": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 创建或更新富文本内容
         * @description 为指定实体、语言和内容类型创建或更新富文本内容
         */
        post: operations["post_richtexttabs_content"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/content/batch": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 批量创建或更新内容
         * @description 批量为指定实体和语言创建或更新多个内容类型的富文本内容
         */
        post: operations["post_richtexttabs_content_batch"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/health/preset-configs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 检查预设配置完整性
         * @description 检查 Rich Text Tabs 预设配置是否完整
         */
        get: operations["get_richtexttabs_health_presetconfigs"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rich-text-tabs/repair/{entity_type}/{entity_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 修复实体富文本内容
         * @description 检查并修复特定实体缺失的富文本内容记录
         */
        post: operations["post_richtexttabs_repair_entity_type_entity_id"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/events": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 展会列表（分页 & 搜索） */
        get: operations["get_admin_events"];
        put?: never;
        /** 创建展会 */
        post: operations["post_admin_events"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/events/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 展会详情 */
        get: operations["get_admin_events_id"];
        put?: never;
        post?: never;
        /** 删除展会 */
        delete: operations["delete_admin_events_id"];
        options?: never;
        head?: never;
        /** 更新展会 */
        patch: operations["patch_admin_events_id"];
        trace?: never;
    };
    "/admin/circles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 社团列表 */
        get: operations["get_admin_circles"];
        put?: never;
        /** 创建社团 */
        post: operations["post_admin_circles"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/circles/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 社团详情 */
        get: operations["get_admin_circles_id"];
        /** 更新社团 */
        put: operations["put_admin_circles_id"];
        post?: never;
        /** 删除社团 */
        delete: operations["delete_admin_circles_id"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/venues": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 场馆列表（管理员） */
        get: operations["get_admin_venues"];
        put?: never;
        /** 创建场馆 */
        post: operations["post_admin_venues"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/venues/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 场馆详情（管理员） */
        get: operations["get_admin_venues_id"];
        /** 更新场馆 */
        put: operations["put_admin_venues_id"];
        post?: never;
        /** 删除场馆 */
        delete: operations["delete_admin_venues_id"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/images/upload": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 上传图片 */
        post: operations["post_admin_images_upload"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/images": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /** 删除图片 */
        delete: operations["delete_admin_images"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/images/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取图片详细信息 */
        get: operations["get_admin_images_id"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 用户列表 */
        get: operations["get_admin_users"];
        put?: never;
        /** 创建用户 */
        post: operations["post_admin_users"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/users/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 用户详情 */
        get: operations["get_admin_users_id"];
        /** 更新用户 */
        put: operations["put_admin_users_id"];
        post?: never;
        /** 删除用户 */
        delete: operations["delete_admin_users_id"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/logs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 查询操作日志 */
        get: operations["get_admin_logs"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/admin/stats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 后台统计数据 */
        get: operations["get_admin_stats"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /**
         * @description 业务错误码
         * @enum {integer}
         */
        ErrorCodes: 10001 | 10002 | 10003 | 20001 | 20002 | 30001 | 30002 | 40001 | 40002 | 40003 | 50001 | 50002 | 60001 | 60002 | 60003;
        ErrorResponse: {
            code: components["schemas"]["ErrorCodes"];
            message: string;
            detail?: unknown;
            requestId: string;
        };
        PaginatedResult: {
            /** @example 120 */
            total: number;
            /** @example 1 */
            page: number;
            /** @example 20 */
            pageSize: number;
            items: {
                /** @example uuid-123 */
                id: string;
                /** @example Reitaisai 22 */
                name_en: string;
                /** @example 第二十二回博麗神社例大祭 */
                name_ja: string;
                /** @example 第二十二回博丽神社例大祭 */
                name_zh: string;
                /** @example May 3, 2025 (Sat) 10:30 – 15:30 */
                date_en: string;
                /** @example 2025年5月3日(土・祝) 10:30 – 15:30 */
                date_ja: string;
                /** @example 2025年5月3日(周六) 10:30 – 15:30 */
                date_zh: string;
                /** @example 20250503 */
                date_sort?: number;
                image_url?: string | null;
                /** @example tokyo-big-sight */
                venue_id: string;
                url?: string | null;
                created_at?: string;
                updated_at?: string;
            }[];
        };
        SuccessResponse: {
            /** @enum {number} */
            code: 0;
            message: string;
            data?: unknown;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    post_auth_signup_email: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /**
                     * Format: email
                     * @description User email address
                     * @example <EMAIL>
                     */
                    email: string;
                    /**
                     * @description User password (minimum 8 characters)
                     * @example password123
                     */
                    password: string;
                    /**
                     * @description User display name
                     * @example Alice
                     */
                    name?: string;
                    /**
                     * @description Unique username (3-30 characters)
                     * @example alice_user
                     */
                    username?: string;
                };
            };
        };
        responses: {
            /** @description User registered successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        user: {
                            /** @example uuid-123 */
                            id: string;
                            /**
                             * Format: email
                             * @example <EMAIL>
                             */
                            email: string;
                            /** @example Alice */
                            name: string;
                            /** @example alice_user */
                            username?: string;
                            /**
                             * @example user
                             * @enum {string}
                             */
                            role: "admin" | "editor" | "viewer" | "user";
                            /** @example false */
                            emailVerified: boolean;
                            /**
                             * Format: date-time
                             * @example 2024-01-01T00:00:00.000Z
                             */
                            createdAt: string;
                            /**
                             * Format: date-time
                             * @example 2024-01-01T00:00:00.000Z
                             */
                            updatedAt: string;
                        };
                        session: {
                            /** @example session-123 */
                            id: string;
                            /** @example uuid-123 */
                            userId: string;
                            /**
                             * Format: date-time
                             * @example 2024-02-01T00:00:00.000Z
                             */
                            expiresAt: string;
                            /** @example *********** */
                            ipAddress?: string;
                            /** @example Mozilla/5.0... */
                            userAgent?: string;
                        };
                    };
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Email or username already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example Email already exists */
                        error: string;
                    };
                };
            };
        };
    };
    post_auth_signin_email: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /**
                     * Format: email
                     * @description User email address
                     * @example <EMAIL>
                     */
                    email: string;
                    /**
                     * @description User password
                     * @example password123
                     */
                    password: string;
                };
            };
        };
        responses: {
            /** @description Login successful */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        user: {
                            /** @example uuid-123 */
                            id: string;
                            /**
                             * Format: email
                             * @example <EMAIL>
                             */
                            email: string;
                            /** @example Alice */
                            name: string;
                            /** @example alice_user */
                            username?: string;
                            /**
                             * @example user
                             * @enum {string}
                             */
                            role: "admin" | "editor" | "viewer" | "user";
                            /** @example false */
                            emailVerified: boolean;
                            /**
                             * Format: date-time
                             * @example 2024-01-01T00:00:00.000Z
                             */
                            createdAt: string;
                            /**
                             * Format: date-time
                             * @example 2024-01-01T00:00:00.000Z
                             */
                            updatedAt: string;
                        };
                        session: {
                            /** @example session-123 */
                            id: string;
                            /** @example uuid-123 */
                            userId: string;
                            /**
                             * Format: date-time
                             * @example 2024-02-01T00:00:00.000Z
                             */
                            expiresAt: string;
                            /** @example *********** */
                            ipAddress?: string;
                            /** @example Mozilla/5.0... */
                            userAgent?: string;
                        };
                    };
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Invalid credentials */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example Invalid email or password */
                        error: string;
                    };
                };
            };
        };
    };
    post_auth_signin_username: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /**
                     * @description Username
                     * @example alice_user
                     */
                    username: string;
                    /**
                     * @description User password
                     * @example password123
                     */
                    password: string;
                };
            };
        };
        responses: {
            /** @description Login successful */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        user: {
                            /** @example uuid-123 */
                            id: string;
                            /**
                             * Format: email
                             * @example <EMAIL>
                             */
                            email: string;
                            /** @example Alice */
                            name: string;
                            /** @example alice_user */
                            username?: string;
                            /**
                             * @example user
                             * @enum {string}
                             */
                            role: "admin" | "editor" | "viewer" | "user";
                            /** @example false */
                            emailVerified: boolean;
                            /**
                             * Format: date-time
                             * @example 2024-01-01T00:00:00.000Z
                             */
                            createdAt: string;
                            /**
                             * Format: date-time
                             * @example 2024-01-01T00:00:00.000Z
                             */
                            updatedAt: string;
                        };
                        session: {
                            /** @example session-123 */
                            id: string;
                            /** @example uuid-123 */
                            userId: string;
                            /**
                             * Format: date-time
                             * @example 2024-02-01T00:00:00.000Z
                             */
                            expiresAt: string;
                            /** @example *********** */
                            ipAddress?: string;
                            /** @example Mozilla/5.0... */
                            userAgent?: string;
                        };
                    };
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Invalid credentials */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example Invalid username or password */
                        error: string;
                    };
                };
            };
        };
    };
    post_auth_signout: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": Record<string, never>;
            };
        };
        responses: {
            /** @description Logout successful */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example true */
                        success: boolean;
                    };
                };
            };
            /** @description Bad Request - Session not found or invalid */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    get_auth_getsession: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Session retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        user: {
                            /** @example uuid-123 */
                            id: string;
                            /**
                             * Format: email
                             * @example <EMAIL>
                             */
                            email: string;
                            /** @example Alice */
                            name: string;
                            /** @example alice_user */
                            username?: string;
                            /**
                             * @example user
                             * @enum {string}
                             */
                            role: "admin" | "editor" | "viewer" | "user";
                            /** @example false */
                            emailVerified: boolean;
                            /**
                             * Format: date-time
                             * @example 2024-01-01T00:00:00.000Z
                             */
                            createdAt: string;
                            /**
                             * Format: date-time
                             * @example 2024-01-01T00:00:00.000Z
                             */
                            updatedAt: string;
                        };
                        session: {
                            /** @example session-123 */
                            id: string;
                            /** @example uuid-123 */
                            userId: string;
                            /**
                             * Format: date-time
                             * @example 2024-02-01T00:00:00.000Z
                             */
                            expiresAt: string;
                            /** @example *********** */
                            ipAddress?: string;
                            /** @example Mozilla/5.0... */
                            userAgent?: string;
                        };
                    };
                };
            };
            /** @description Not authenticated */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example Not authenticated */
                        error: string;
                    };
                };
            };
        };
    };
    get_events: {
        parameters: {
            query?: {
                page?: string;
                pageSize?: string;
                keyword?: string;
                date_from?: string;
                date_to?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 展会列表 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaginatedResult"];
                };
            };
        };
    };
    get_events_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 展会详情 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example uuid-123 */
                        id: string;
                        /** @example Reitaisai 22 */
                        name_en: string;
                        /** @example 第二十二回博麗神社例大祭 */
                        name_ja: string;
                        /** @example 第二十二回博丽神社例大祭 */
                        name_zh: string;
                        /** @example May 3, 2025 (Sat) 10:30 – 15:30 */
                        date_en: string;
                        /** @example 2025年5月3日(土・祝) 10:30 – 15:30 */
                        date_ja: string;
                        /** @example 2025年5月3日(周六) 10:30 – 15:30 */
                        date_zh: string;
                        /** @example 20250503 */
                        date_sort?: number;
                        image_url?: string | null;
                        /** @example tokyo-big-sight */
                        venue_id: string;
                        url?: string | null;
                        created_at?: string;
                        updated_at?: string;
                    };
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_events_id_circles: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 社团列表 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example uuid-123 */
                        id: string;
                        /** @example Reitaisai 22 */
                        name_en: string;
                        /** @example 第二十二回博麗神社例大祭 */
                        name_ja: string;
                        /** @example 第二十二回博丽神社例大祭 */
                        name_zh: string;
                        /** @example May 3, 2025 (Sat) 10:30 – 15:30 */
                        date_en: string;
                        /** @example 2025年5月3日(土・祝) 10:30 – 15:30 */
                        date_ja: string;
                        /** @example 2025年5月3日(周六) 10:30 – 15:30 */
                        date_zh: string;
                        /** @example 20250503 */
                        date_sort?: number;
                        image_url?: string | null;
                        /** @example tokyo-big-sight */
                        venue_id: string;
                        url?: string | null;
                        created_at?: string;
                        updated_at?: string;
                        /** @example あ01a */
                        booth_id?: string | null;
                    }[];
                };
            };
        };
    };
    get_events_id_appearances: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 参展记录分页 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaginatedResult"] & {
                        /** @example 120 */
                        total?: number;
                        /** @example 1 */
                        page?: number;
                        /** @example 20 */
                        pageSize?: number;
                        items?: unknown[];
                    };
                };
            };
        };
    };
    get_venues: {
        parameters: {
            query?: {
                page?: string;
                pageSize?: string;
                keyword?: string;
                city?: string;
                capacity_min?: string;
                capacity_max?: string;
                has_parking?: string;
                has_wifi?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 场馆列表 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaginatedResult"] & {
                        /** @example 120 */
                        total?: number;
                        /** @example 1 */
                        page?: number;
                        /** @example 20 */
                        pageSize?: number;
                        items?: {
                            /** @example tokyo-big-sight */
                            id: string;
                            /** @example Tokyo Big Sight */
                            name: string;
                            address?: string | null;
                            description?: string | null;
                            /** @example 35.6298 */
                            lat: number;
                            /** @example 139.793 */
                            lng: number;
                            capacity?: number | null;
                            website_url?: string | null;
                            phone?: string | null;
                            facilities?: string | null;
                            transportation?: string | null;
                            parking_info?: string | null;
                            created_at?: string;
                            updated_at?: string;
                        }[];
                    };
                };
            };
        };
    };
    get_venues_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 场馆详情 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example tokyo-big-sight */
                        id: string;
                        /** @example Tokyo Big Sight */
                        name: string;
                        address?: string | null;
                        description?: string | null;
                        /** @example 35.6298 */
                        lat: number;
                        /** @example 139.793 */
                        lng: number;
                        capacity?: number | null;
                        website_url?: string | null;
                        phone?: string | null;
                        facilities?: string | null;
                        transportation?: string | null;
                        parking_info?: string | null;
                        created_at?: string;
                        updated_at?: string;
                    };
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_circles: {
        parameters: {
            query?: {
                page?: string;
                pageSize?: string;
                search?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 社团列表 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaginatedResult"] & {
                        /** @example 120 */
                        total?: number;
                        /** @example 1 */
                        page?: number;
                        /** @example 20 */
                        pageSize?: number;
                        items?: {
                            /** @example uuid-123 */
                            id: string;
                            /** @example 東方愛好会 */
                            name: string;
                            /** @example {"author":"Alice","twitter_url":"https://twitter.com/example"} */
                            urls?: string | null;
                            /** @example 2024-01-01T00:00:00Z */
                            created_at?: string;
                            /** @example 2024-01-01T00:00:00Z */
                            updated_at?: string;
                        }[];
                    };
                };
            };
        };
    };
    get_circles_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 社团详情 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example uuid-123 */
                        id: string;
                        /** @example 東方愛好会 */
                        name: string;
                        /** @example {"author":"Alice","twitter_url":"https://twitter.com/example"} */
                        urls?: string | null;
                        /** @example 2024-01-01T00:00:00Z */
                        created_at?: string;
                        /** @example 2024-01-01T00:00:00Z */
                        updated_at?: string;
                    };
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_circles_id_appearances: {
        parameters: {
            query?: {
                page?: string;
                pageSize?: string;
            };
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 参展记录分页 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaginatedResult"] & {
                        /** @example 120 */
                        total?: number;
                        /** @example 1 */
                        page?: number;
                        /** @example 20 */
                        pageSize?: number;
                        items?: unknown[];
                    };
                };
            };
        };
    };
    post_circles_circleId_bookmark: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                circleId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 收藏状态已切换 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"] & {
                        data: {
                            isBookmarked: boolean;
                        };
                    };
                };
            };
            /** @description 未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    get_circles_circleId_bookmark_status: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                circleId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取收藏状态成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"] & {
                        data: {
                            /** @example true */
                            isBookmarked: boolean;
                            /** @example bookmark-uuid */
                            bookmarkId: string | null;
                            /** @example 2025-01-01T00:00:00Z */
                            createdAt: string | null;
                        };
                    };
                };
            };
            /** @description 未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    get_user_bookmarks: {
        parameters: {
            query?: {
                page?: number;
                pageSize?: number;
                cursor?: string;
                search?: string;
                sortBy?: "created_at" | "circle_name";
                sortOrder?: "asc" | "desc";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取收藏列表成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"] & {
                        data: {
                            items: {
                                /** @example bookmark-uuid */
                                id: string;
                                /** @example 2025-01-01T00:00:00Z */
                                created_at: string;
                                circle: {
                                    /** @example circle-uuid */
                                    id: string;
                                    /** @example 某某工作室 */
                                    name: string;
                                    /** @example {"twitter":"@example"} */
                                    urls: string | null;
                                    /** @example 2025-01-01T00:00:00Z */
                                    created_at: string;
                                    /** @example 2025-01-01T00:00:00Z */
                                    updated_at: string;
                                };
                            }[];
                            /** @example 15 */
                            total: number;
                            /** @example 1 */
                            page: number;
                            /** @example 20 */
                            pageSize: number;
                            /** @example 1 */
                            totalPages: number;
                            /**
                             * @description 下一页的游标，为null表示没有更多数据
                             * @example eyJjcmVhdGVkX2F0IjoiMjAyNS0wMS0wMVQwMDowMDowMFoifQ==
                             */
                            nextCursor: string | null;
                            /**
                             * @description 是否还有更多数据
                             * @example true
                             */
                            hasMore: boolean;
                        };
                    };
                };
            };
            /** @description 未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    get_user_bookmarks_stats: {
        parameters: {
            query?: {
                includeIds?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取收藏统计成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"] & {
                        data: {
                            /** @example 15 */
                            totalBookmarks: number;
                            /** @example 3 */
                            recentBookmarks: number;
                            /** @example {
                             *       "original": 8,
                             *       "derivative": 7
                             *     } */
                            categoryCounts: {
                                [key: string]: number;
                            };
                            /**
                             * @description 用户收藏的所有社团ID列表，仅在 includeIds=true 时返回，用于前端快速收藏状态检查
                             * @example [
                             *       "circle-1",
                             *       "circle-2",
                             *       "circle-3"
                             *     ]
                             */
                            bookmarkedCircleIds?: string[];
                        };
                    };
                };
            };
            /** @description 未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    post_user_bookmarks_batch: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /**
                     * @example add
                     * @enum {string}
                     */
                    action: "add" | "remove";
                    /** @example [
                     *       "circle-1",
                     *       "circle-2",
                     *       "circle-3"
                     *     ] */
                    circleIds: string[];
                };
            };
        };
        responses: {
            /** @description 批量操作成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"] & {
                        data: {
                            /** @example [
                             *       "circle-1",
                             *       "circle-2"
                             *     ] */
                            success: string[];
                            /** @example [
                             *       {
                             *         "circleId": "circle-3",
                             *         "reason": "社团不存在"
                             *       }
                             *     ] */
                            failed: {
                                /** @example circle-3 */
                                circleId: string;
                                /** @example 社团不存在 */
                                reason: string;
                            }[];
                            /** @example 3 */
                            total: number;
                            /** @example 2 */
                            successCount: number;
                            /** @example 1 */
                            failedCount: number;
                        };
                    };
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    get_artists: {
        parameters: {
            query?: {
                page?: string;
                pageSize?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 作者列表 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaginatedResult"] & {
                        /** @example 120 */
                        total?: number;
                        /** @example 1 */
                        page?: number;
                        /** @example 20 */
                        pageSize?: number;
                        items?: {
                            /** @example uuid-123 */
                            id: string;
                            /** @example Alice */
                            name: string;
                            urls?: string | null;
                            /** @example 2024-01-01T00:00:00Z */
                            created_at: string;
                            /** @example 2024-01-01T00:00:00Z */
                            updated_at: string;
                            description?: string | null;
                        }[];
                    };
                };
            };
        };
    };
    get_artists_id: {
        parameters: {
            query?: {
                lang?: string;
            };
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 作者详情 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example uuid-123 */
                        id: string;
                        /** @example Alice */
                        name: string;
                        urls?: string | null;
                        /** @example 2024-01-01T00:00:00Z */
                        created_at: string;
                        /** @example 2024-01-01T00:00:00Z */
                        updated_at: string;
                        description?: string | null;
                    };
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_appearances: {
        parameters: {
            query?: {
                circle_id?: string;
                event_id?: string;
                page?: string;
                pageSize?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 参展记录 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaginatedResult"] & {
                        /** @example 120 */
                        total?: number;
                        /** @example 1 */
                        page?: number;
                        /** @example 20 */
                        pageSize?: number;
                        items?: {
                            /** @example uuid-123 */
                            id: string;
                            /** @example circle-uuid */
                            circle_id: string;
                            /** @example event-uuid */
                            event_id: string;
                            /** @example artist-uuid */
                            artist_id?: string | null;
                            /** @example A01a */
                            booth_id: string;
                            /** @example /2025/05/03/A1.jpg */
                            path?: string | null;
                            /** @example 2024-01-01T00:00:00Z */
                            created_at: string;
                            /** @example 2024-01-01T00:00:00Z */
                            updated_at: string;
                        }[];
                    };
                };
            };
        };
    };
    post_appearances: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @example uuid-123 */
                    id?: string;
                    /** @example circle-uuid */
                    circle_id?: string;
                    /** @example event-uuid */
                    event_id?: string;
                    /** @example artist-uuid */
                    artist_id?: string | null;
                    /** @example A01a */
                    booth_id?: string;
                    /** @example /2025/05/03/A1.jpg */
                    path?: string | null;
                };
            };
        };
        responses: {
            /** @description success */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        success: boolean;
                    };
                };
            };
        };
    };
    get_appearances_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 详情 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example uuid-123 */
                        id: string;
                        /** @example circle-uuid */
                        circle_id: string;
                        /** @example event-uuid */
                        event_id: string;
                        /** @example artist-uuid */
                        artist_id?: string | null;
                        /** @example A01a */
                        booth_id: string;
                        /** @example /2025/05/03/A1.jpg */
                        path?: string | null;
                        /** @example 2024-01-01T00:00:00Z */
                        created_at: string;
                        /** @example 2024-01-01T00:00:00Z */
                        updated_at: string;
                    };
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    delete_appearances_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description success */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        success: boolean;
                    };
                };
            };
        };
    };
    get_search: {
        parameters: {
            query: {
                q: string;
                type?: "all" | "events" | "circles";
                page?: string;
                limit?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 搜索结果 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example true */
                        success: boolean;
                        data: {
                            /**
                             * @description 结果类型
                             * @example event
                             * @enum {string}
                             */
                            type: "event" | "circle";
                            /**
                             * @description 资源ID
                             * @example 550e8400-e29b-41d4-a716-446655440000
                             */
                            id: string;
                            /**
                             * @description 名称
                             * @example Comiket 103
                             */
                            name: string;
                            /**
                             * @description 描述
                             * @example 世界最大的同人志即卖会
                             */
                            description: string | null;
                            /**
                             * @description 场馆名称（仅事件类型）
                             * @example 东京国际展示场
                             */
                            venue_name?: string | null;
                            /**
                             * @description 开始时间（仅事件类型）
                             * @example 2024-12-30T10:00:00Z
                             */
                            start_date?: string | null;
                            /**
                             * @description 图片URL
                             * @example https://example.com/comiket103.jpg
                             */
                            image_url?: string | null;
                            /**
                             * @description 搜索相关性评分
                             * @example 0.8567
                             */
                            rank: number;
                        }[];
                        /**
                         * @description 响应语言
                         * @example zh
                         */
                        locale: string;
                        /**
                         * @description 响应时间戳
                         * @example 2024-01-15T10:30:00.000Z
                         */
                        timestamp: string;
                        meta: {
                            /**
                             * @description 总结果数
                             * @example 15
                             */
                            total: number;
                            /**
                             * @description 搜索关键词
                             * @example Comiket
                             */
                            query: string;
                            /**
                             * @description 搜索类型
                             * @example events
                             */
                            type: string;
                        };
                    };
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_feed: {
        parameters: {
            query?: {
                page?: string;
                limit?: string;
                type?: "all" | "events" | "circles";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Feed 流数据 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example true */
                        success: boolean;
                        data: {
                            /**
                             * @description Feed项ID
                             * @example feed-001
                             */
                            id: string;
                            /**
                             * @description 内容类型
                             * @example event
                             * @enum {string}
                             */
                            type: "event" | "circle";
                            content: {
                                /**
                                 * @description 资源ID
                                 * @example 550e8400-e29b-41d4-a716-446655440000
                                 */
                                id: string;
                                /**
                                 * @description 名称
                                 * @example Comiket 103
                                 */
                                name: string;
                                /**
                                 * @description 描述
                                 * @example 世界最大的同人志即卖会
                                 */
                                description: string | null;
                                /**
                                 * @description 开始时间（仅事件类型）
                                 * @example 2024-12-30T10:00:00Z
                                 */
                                start_date?: string | null;
                                /**
                                 * @description 图片URL
                                 * @example https://example.com/comiket103.jpg
                                 */
                                image_url?: string | null;
                            };
                            /**
                             * @description 创建时间
                             * @example 2024-01-15T08:00:00Z
                             */
                            created_at: string;
                        }[];
                        /**
                         * @description 响应语言
                         * @example zh
                         */
                        locale: string;
                        /**
                         * @description 响应时间戳
                         * @example 2024-01-15T10:30:00.000Z
                         */
                        timestamp: string;
                        meta: {
                            /**
                             * @description 总数量
                             * @example 200
                             */
                            total: number;
                            /**
                             * @description 当前页码
                             * @example 1
                             */
                            page: number;
                            /**
                             * @description 每页数量
                             * @example 20
                             */
                            limit: number;
                            /**
                             * @description 是否有更多数据
                             * @example true
                             */
                            hasMore: boolean;
                        };
                    };
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_images_batch: {
        parameters: {
            query: {
                events: string;
                variant?: "original" | "large" | "medium" | "thumb";
                imageType?: "poster" | "logo" | "banner" | "gallery";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 批量查询结果 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example 200 */
                        code: number;
                        /** @example 批量查询成功 */
                        message: string;
                        /** @example {
                         *       "event1": {
                         *         "id": "img-456",
                         *         "variant": "medium",
                         *         "file_path": "/images/events/event1/poster_medium.jpg"
                         *       },
                         *       "event2": {
                         *         "id": "img-789",
                         *         "variant": "medium",
                         *         "file_path": "/images/events/event2/poster_medium.jpg"
                         *       },
                         *       "event3": null
                         *     } */
                        data: {
                            [key: string]: {
                                /** @example uuid-123 */
                                id: string;
                                /** @example group-uuid-456 */
                                group_id: string;
                                /**
                                 * @example event
                                 * @enum {string}
                                 */
                                resource_type: "event" | "circle" | "venue";
                                /** @example resource-uuid-789 */
                                resource_id: string;
                                /**
                                 * @example poster
                                 * @enum {string}
                                 */
                                image_type: "poster" | "logo" | "banner" | "gallery";
                                /**
                                 * @example thumb
                                 * @enum {string}
                                 */
                                variant: "original" | "large" | "medium" | "thumb";
                                /** @example /images/events/reitaisai-22/poster_thumb.jpg */
                                file_path: string;
                                /** @example 51200 */
                                file_size?: number | null;
                                /** @example 200 */
                                width?: number | null;
                                /** @example 300 */
                                height?: number | null;
                                /** @example jpeg */
                                format?: string | null;
                                /** @example 2025-01-30T00:00:00Z */
                                created_at?: string;
                                /** @example 2025-01-30T00:00:00Z */
                                updated_at?: string;
                            } | null;
                        };
                    };
                };
            };
            /** @description 参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_images_id_file: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 图片文件 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "image/*": string;
                };
            };
            /** @description 图片不存在 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_images_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 图片信息 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example 0 */
                        code: number;
                        /** @example OK */
                        message: string;
                        data: {
                            /** @example uuid-123 */
                            id: string;
                            /** @example group-uuid-456 */
                            group_id: string;
                            /**
                             * @example event
                             * @enum {string}
                             */
                            resource_type: "event" | "circle" | "venue";
                            /** @example resource-uuid-789 */
                            resource_id: string;
                            /**
                             * @example poster
                             * @enum {string}
                             */
                            image_type: "poster" | "logo" | "banner" | "gallery";
                            /**
                             * @example thumb
                             * @enum {string}
                             */
                            variant: "original" | "large" | "medium" | "thumb";
                            /** @example /images/events/reitaisai-22/poster_thumb.jpg */
                            file_path: string;
                            /** @example 51200 */
                            file_size?: number | null;
                            /** @example 200 */
                            width?: number | null;
                            /** @example 300 */
                            height?: number | null;
                            /** @example jpeg */
                            format?: string | null;
                            /** @example 2025-01-30T00:00:00Z */
                            created_at?: string;
                            /** @example 2025-01-30T00:00:00Z */
                            updated_at?: string;
                        };
                    };
                };
            };
            /** @description 图片不存在 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_images_category_resourceId: {
        parameters: {
            query?: {
                page?: string;
                pageSize?: string;
                variant?: "original" | "large" | "medium" | "thumb";
                imageType?: "poster" | "logo" | "banner" | "gallery";
            };
            header?: never;
            path: {
                category: "event" | "circle" | "venue";
                resourceId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 图片列表 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example 0 */
                        code: number;
                        /** @example OK */
                        message: string;
                        data: {
                            images: {
                                /** @example uuid-123 */
                                id: string;
                                /** @example group-uuid-456 */
                                group_id: string;
                                /**
                                 * @example event
                                 * @enum {string}
                                 */
                                resource_type: "event" | "circle" | "venue";
                                /** @example resource-uuid-789 */
                                resource_id: string;
                                /**
                                 * @example poster
                                 * @enum {string}
                                 */
                                image_type: "poster" | "logo" | "banner" | "gallery";
                                /**
                                 * @example thumb
                                 * @enum {string}
                                 */
                                variant: "original" | "large" | "medium" | "thumb";
                                /** @example /images/events/reitaisai-22/poster_thumb.jpg */
                                file_path: string;
                                /** @example 51200 */
                                file_size?: number | null;
                                /** @example 200 */
                                width?: number | null;
                                /** @example 300 */
                                height?: number | null;
                                /** @example jpeg */
                                format?: string | null;
                                /** @example 2025-01-30T00:00:00Z */
                                created_at?: string;
                                /** @example 2025-01-30T00:00:00Z */
                                updated_at?: string;
                            }[];
                            pagination: {
                                /** @example 1 */
                                page: number;
                                /** @example 20 */
                                pageSize: number;
                                /** @example 100 */
                                total: number;
                                /** @example 5 */
                                totalPages: number;
                            };
                        };
                    };
                };
            };
        };
    };
    get_richtext_entityType_entityId_content: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                entityType: "event" | "venue";
                entityId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example content-uuid-123 */
                        id: string;
                        /**
                         * @example event
                         * @enum {string}
                         */
                        entity_type: "event" | "venue";
                        /** @example reitaisai-22 */
                        entity_id: string;
                        /**
                         * @example en
                         * @enum {string}
                         */
                        language_code: "en" | "zh" | "ja";
                        /** @example introduction */
                        content_type: string;
                        /** @example {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]} */
                        content: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        created_at: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        updated_at: string;
                    };
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 实体不存在 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    put_richtext_entityType_entityId_content: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                entityType: "event" | "venue";
                entityId: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    contents: {
                        /** @enum {string} */
                        entity_type: "event" | "venue";
                        entity_id: string;
                        /** @enum {string} */
                        language_code: "en" | "zh" | "ja";
                        content_type: string;
                        content: string;
                    }[];
                };
            };
        };
        responses: {
            /** @description 更新成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example content-uuid-123 */
                        id: string;
                        /**
                         * @example event
                         * @enum {string}
                         */
                        entity_type: "event" | "venue";
                        /** @example reitaisai-22 */
                        entity_id: string;
                        /**
                         * @example en
                         * @enum {string}
                         */
                        language_code: "en" | "zh" | "ja";
                        /** @example introduction */
                        content_type: string;
                        /** @example {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]} */
                        content: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        created_at: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        updated_at: string;
                    };
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    post_richtext_entityType_entityId_content: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                entityType: "event" | "venue";
                entityId: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @enum {string} */
                    entity_type: "event" | "venue";
                    entity_id: string;
                    /** @enum {string} */
                    language_code: "en" | "zh" | "ja";
                    content_type: string;
                    content: string;
                };
            };
        };
        responses: {
            /** @description 创建成功 */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example content-uuid-123 */
                        id: string;
                        /**
                         * @example event
                         * @enum {string}
                         */
                        entity_type: "event" | "venue";
                        /** @example reitaisai-22 */
                        entity_id: string;
                        /**
                         * @example en
                         * @enum {string}
                         */
                        language_code: "en" | "zh" | "ja";
                        /** @example introduction */
                        content_type: string;
                        /** @example {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]} */
                        content: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        created_at: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        updated_at: string;
                    };
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    delete_richtext_entityType_entityId_content: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                entityType: "event" | "venue";
                entityId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 删除成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_richtext_entityType_entityId_content_contentType: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                entityType: "event" | "venue";
                entityId: string;
                contentType: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        content?: string;
                    };
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 内容不存在 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    post_richtext_api_upload_images: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "multipart/form-data": {
                    /**
                     * Format: binary
                     * @description 图片文件
                     */
                    image: string;
                };
            };
        };
        responses: {
            /** @description 上传成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /**
                         * @description 图片访问URL
                         * @example /images/content/1640995200000_example.jpg
                         */
                        url?: string;
                    };
                };
            };
            /** @description 请求参数错误或文件验证失败 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 上传失败 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_richtexttabs_configs_entityType_languageCode: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                entityType: "event" | "venue";
                languageCode: "en" | "zh" | "ja";
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example config-uuid-123 */
                        id: string;
                        /**
                         * @example event
                         * @enum {string}
                         */
                        entity_type: "event" | "venue";
                        /** @example entity-uuid-456 */
                        entity_id?: string;
                        /**
                         * @example en
                         * @enum {string}
                         */
                        language_code: "en" | "zh" | "ja";
                        /** @example introduction */
                        key: string;
                        /** @example Introduction */
                        label: string;
                        /** @example Enter introduction... */
                        placeholder?: string;
                        /** @example info */
                        icon?: string;
                        /** @example 0 */
                        sort_order: number;
                        /** @example true */
                        is_active: boolean;
                        /** @example false */
                        is_preset: boolean;
                        deleted_at?: string;
                        deleted_by?: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        created_at: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        updated_at: string;
                    }[];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_richtexttabs_configs_entityType_entityId_languageCode_all: {
        parameters: {
            query?: {
                includeDeleted?: string;
            };
            header?: never;
            path: {
                entityType: "event" | "venue";
                entityId: string;
                languageCode: "en" | "zh" | "ja";
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example config-uuid-123 */
                        id: string;
                        /**
                         * @example event
                         * @enum {string}
                         */
                        entity_type: "event" | "venue";
                        /** @example entity-uuid-456 */
                        entity_id?: string;
                        /**
                         * @example en
                         * @enum {string}
                         */
                        language_code: "en" | "zh" | "ja";
                        /** @example introduction */
                        key: string;
                        /** @example Introduction */
                        label: string;
                        /** @example Enter introduction... */
                        placeholder?: string;
                        /** @example info */
                        icon?: string;
                        /** @example 0 */
                        sort_order: number;
                        /** @example true */
                        is_active: boolean;
                        /** @example false */
                        is_preset: boolean;
                        deleted_at?: string;
                        deleted_by?: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        created_at: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        updated_at: string;
                    }[];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    post_richtexttabs_configs: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @enum {string} */
                    entity_type: "event" | "venue";
                    entity_id: string;
                    /** @enum {string} */
                    language_code: "en" | "zh" | "ja";
                    label: string;
                    placeholder?: string;
                    icon?: string;
                    sort_order?: number;
                    is_active?: boolean;
                    is_preset?: boolean;
                };
            };
        };
        responses: {
            /** @description 创建成功 */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example config-uuid-123 */
                        id: string;
                        /**
                         * @example event
                         * @enum {string}
                         */
                        entity_type: "event" | "venue";
                        /** @example entity-uuid-456 */
                        entity_id?: string;
                        /**
                         * @example en
                         * @enum {string}
                         */
                        language_code: "en" | "zh" | "ja";
                        /** @example introduction */
                        key: string;
                        /** @example Introduction */
                        label: string;
                        /** @example Enter introduction... */
                        placeholder?: string;
                        /** @example info */
                        icon?: string;
                        /** @example 0 */
                        sort_order: number;
                        /** @example true */
                        is_active: boolean;
                        /** @example false */
                        is_preset: boolean;
                        deleted_at?: string;
                        deleted_by?: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        created_at: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        updated_at: string;
                    };
                };
            };
            /** @description 请求参数错误或验证失败 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    put_richtexttabs_configs_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    label?: string;
                    placeholder?: string;
                    icon?: string;
                    sort_order?: number;
                    is_active?: boolean;
                };
            };
        };
        responses: {
            /** @description 更新成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example config-uuid-123 */
                        id: string;
                        /**
                         * @example event
                         * @enum {string}
                         */
                        entity_type: "event" | "venue";
                        /** @example entity-uuid-456 */
                        entity_id?: string;
                        /**
                         * @example en
                         * @enum {string}
                         */
                        language_code: "en" | "zh" | "ja";
                        /** @example introduction */
                        key: string;
                        /** @example Introduction */
                        label: string;
                        /** @example Enter introduction... */
                        placeholder?: string;
                        /** @example info */
                        icon?: string;
                        /** @example 0 */
                        sort_order: number;
                        /** @example true */
                        is_active: boolean;
                        /** @example false */
                        is_preset: boolean;
                        deleted_at?: string;
                        deleted_by?: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        created_at: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        updated_at: string;
                    };
                };
            };
            /** @description 请求参数错误或验证失败 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 配置不存在 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    delete_richtexttabs_configs_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    deleted_by: string;
                };
            };
        };
        responses: {
            /** @description 删除成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        success: boolean;
                        message: string;
                        affected_count?: number;
                        data?: unknown;
                    };
                };
            };
            /** @description 请求参数错误或预设配置不能删除 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 配置不存在 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    post_richtexttabs_configs_id_restore: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 恢复成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        success: boolean;
                        message: string;
                        affected_count?: number;
                        data?: unknown;
                    };
                };
            };
            /** @description 配置不存在或未删除 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    post_richtexttabs_configs_reorder: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @enum {string} */
                    entity_type: "event" | "venue";
                    /** @enum {string} */
                    language_code: "en" | "zh" | "ja";
                    orders: {
                        id: string;
                        sort_order: number;
                    }[];
                };
            };
        };
        responses: {
            /** @description 排序更新成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        success: boolean;
                        message: string;
                        affected_count?: number;
                        data?: unknown;
                    };
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    post_richtexttabs_configs_batchstatus: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    ids: string[];
                    is_active: boolean;
                };
            };
        };
        responses: {
            /** @description 状态更新成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        success: boolean;
                        message: string;
                        affected_count?: number;
                        data?: unknown;
                    };
                };
            };
            /** @description 请求参数错误或预设配置不能禁用 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_richtexttabs_configs_suggestkeys: {
        parameters: {
            query: {
                label: string;
                entity_type: "event" | "venue";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        code: number;
                        message: string;
                        data: {
                            suggestions: string[];
                            recommended: string;
                        };
                    };
                };
            };
            /** @description 参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_richtexttabs_tabs_entityType_entityId_languageCode: {
        parameters: {
            query?: {
                includeInactive?: string;
            };
            header?: never;
            path: {
                entityType: "event" | "venue";
                entityId: string;
                languageCode: "en" | "zh" | "ja";
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @enum {string} */
                        entity_type: "event" | "venue";
                        entity_id: string;
                        /** @enum {string} */
                        language_code: "en" | "zh" | "ja";
                        tabs: {
                            config: {
                                /** @example config-uuid-123 */
                                id: string;
                                /**
                                 * @example event
                                 * @enum {string}
                                 */
                                entity_type: "event" | "venue";
                                /** @example entity-uuid-456 */
                                entity_id?: string;
                                /**
                                 * @example en
                                 * @enum {string}
                                 */
                                language_code: "en" | "zh" | "ja";
                                /** @example introduction */
                                key: string;
                                /** @example Introduction */
                                label: string;
                                /** @example Enter introduction... */
                                placeholder?: string;
                                /** @example info */
                                icon?: string;
                                /** @example 0 */
                                sort_order: number;
                                /** @example true */
                                is_active: boolean;
                                /** @example false */
                                is_preset: boolean;
                                deleted_at?: string;
                                deleted_by?: string;
                                /** @example 2025-01-04T10:00:00.000Z */
                                created_at: string;
                                /** @example 2025-01-04T10:00:00.000Z */
                                updated_at: string;
                            };
                            content?: {
                                /** @example content-uuid-123 */
                                id: string;
                                /**
                                 * @example event
                                 * @enum {string}
                                 */
                                entity_type: "event" | "venue";
                                /** @example reitaisai-22 */
                                entity_id: string;
                                /**
                                 * @example en
                                 * @enum {string}
                                 */
                                language_code: "en" | "zh" | "ja";
                                /** @example introduction */
                                content_type: string;
                                /** @example {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]} */
                                content: string;
                                /** @example 2025-01-04T10:00:00.000Z */
                                created_at: string;
                                /** @example 2025-01-04T10:00:00.000Z */
                                updated_at: string;
                            };
                        }[];
                    };
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    post_richtexttabs_content: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @enum {string} */
                    entity_type: "event" | "venue";
                    entity_id: string;
                    /** @enum {string} */
                    language_code: "en" | "zh" | "ja";
                    content_type: string;
                    content: string;
                };
            };
        };
        responses: {
            /** @description 保存成功 */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example content-uuid-123 */
                        id: string;
                        /**
                         * @example event
                         * @enum {string}
                         */
                        entity_type: "event" | "venue";
                        /** @example reitaisai-22 */
                        entity_id: string;
                        /**
                         * @example en
                         * @enum {string}
                         */
                        language_code: "en" | "zh" | "ja";
                        /** @example introduction */
                        content_type: string;
                        /** @example {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]} */
                        content: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        created_at: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        updated_at: string;
                    };
                };
            };
            /** @description 请求参数错误或验证失败 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    post_richtexttabs_content_batch: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @enum {string} */
                    entity_type: "event" | "venue";
                    entity_id: string;
                    /** @enum {string} */
                    language_code: "en" | "zh" | "ja";
                    contents: {
                        [key: string]: string;
                    };
                };
            };
        };
        responses: {
            /** @description 批量保存成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example content-uuid-123 */
                        id: string;
                        /**
                         * @example event
                         * @enum {string}
                         */
                        entity_type: "event" | "venue";
                        /** @example reitaisai-22 */
                        entity_id: string;
                        /**
                         * @example en
                         * @enum {string}
                         */
                        language_code: "en" | "zh" | "ja";
                        /** @example introduction */
                        content_type: string;
                        /** @example {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]} */
                        content: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        created_at: string;
                        /** @example 2025-01-04T10:00:00.000Z */
                        updated_at: string;
                    }[];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_richtexttabs_health_presetconfigs: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 检查完成 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        code: number;
                        message: string;
                        data: {
                            isComplete: boolean;
                            missing: string[];
                            existing: number;
                            checked: number;
                        };
                    };
                };
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    post_richtexttabs_repair_entity_type_entity_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                entity_type: "event" | "venue";
                entity_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 修复完成 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        code: number;
                        message: string;
                        data: {
                            entity_type: string;
                            entity_id: string;
                            before: {
                                isComplete: boolean;
                                expectedCount: number;
                                actualCount: number;
                                missingCount: number;
                            };
                            after: {
                                repaired: number;
                                errors: string[];
                            };
                        };
                    };
                };
            };
            /** @description 参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_admin_events: {
        parameters: {
            query?: {
                page?: string;
                pageSize?: string;
                keyword?: string;
                date_from?: string;
                date_to?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 分页列表 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaginatedResult"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    post_admin_events: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @example custom-event-id */
                    id?: string;
                    /** @example Reitaisai 22 */
                    name_en: string;
                    /** @example 第二十二回博麗神社例大祭 */
                    name_ja: string;
                    /** @example 第二十二回博丽神社例大祭 */
                    name_zh: string;
                    /** @example May 3, 2025 (Sat) 10:30 – 15:30 */
                    date_en: string;
                    /** @example 2025年5月3日(土・祝) 10:30 – 15:30 */
                    date_ja: string;
                    /** @example 2025年5月3日(周六) 10:30 – 15:30 */
                    date_zh: string;
                    /** @example 20250503 */
                    date_sort?: number;
                    image_url?: string | null;
                    /** @example tokyo-big-sight */
                    venue_id: string;
                    url?: string | null;
                };
            };
        };
        responses: {
            /** @description 展会创建成功 */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_admin_events_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 展会详情 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example uuid-123 */
                        id: string;
                        /** @example Reitaisai 22 */
                        name_en: string;
                        /** @example 第二十二回博麗神社例大祭 */
                        name_ja: string;
                        /** @example 第二十二回博丽神社例大祭 */
                        name_zh: string;
                        /** @example May 3, 2025 (Sat) 10:30 – 15:30 */
                        date_en: string;
                        /** @example 2025年5月3日(土・祝) 10:30 – 15:30 */
                        date_ja: string;
                        /** @example 2025年5月3日(周六) 10:30 – 15:30 */
                        date_zh: string;
                        /** @example 20250503 */
                        date_sort?: number;
                        image_url?: string | null;
                        /** @example tokyo-big-sight */
                        venue_id: string;
                        url?: string | null;
                        created_at?: string;
                        updated_at?: string;
                    };
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    delete_admin_events_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 展会已删除 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                    };
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    patch_admin_events_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @example custom-event-id */
                    id?: string;
                    /** @example Reitaisai 22 */
                    name_en?: string;
                    /** @example 第二十二回博麗神社例大祭 */
                    name_ja?: string;
                    /** @example 第二十二回博丽神社例大祭 */
                    name_zh?: string;
                    /** @example May 3, 2025 (Sat) 10:30 – 15:30 */
                    date_en?: string;
                    /** @example 2025年5月3日(土・祝) 10:30 – 15:30 */
                    date_ja?: string;
                    /** @example 2025年5月3日(周六) 10:30 – 15:30 */
                    date_zh?: string;
                    /** @example 20250503 */
                    date_sort?: number;
                    image_url?: string | null;
                    /** @example tokyo-big-sight */
                    venue_id?: string;
                    url?: string | null;
                };
            };
        };
        responses: {
            /** @description 展会已保存 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_admin_circles: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 社团列表 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaginatedResult"] & {
                        /** @example 120 */
                        total?: number;
                        /** @example 1 */
                        page?: number;
                        /** @example 20 */
                        pageSize?: number;
                        items?: {
                            /** @example uuid-123 */
                            id: string;
                            /** @example 東方愛好会 */
                            name: string;
                            /** @example {"author":"Alice","twitter_url":"https://twitter.com/example"} */
                            urls?: string | null;
                            /** @example 2024-01-01T00:00:00Z */
                            created_at?: string;
                            /** @example 2024-01-01T00:00:00Z */
                            updated_at?: string;
                        }[];
                    };
                };
            };
        };
    };
    post_admin_circles: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @example 東方愛好会 */
                    name: string;
                    /** @example Alice */
                    author?: string;
                    /** @example https://twitter.com/example */
                    twitter_url?: string;
                    /** @example https://pixiv.net/users/123 */
                    pixiv_url?: string;
                    /** @example https://example.com */
                    web_url?: string;
                };
            };
        };
        responses: {
            /** @description 社团创建成功 */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_admin_circles_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 社团详情 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example uuid-123 */
                        id: string;
                        /** @example 東方愛好会 */
                        name: string;
                        /** @example {"author":"Alice","twitter_url":"https://twitter.com/example"} */
                        urls?: string | null;
                        /** @example 2024-01-01T00:00:00Z */
                        created_at?: string;
                        /** @example 2024-01-01T00:00:00Z */
                        updated_at?: string;
                    };
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    put_admin_circles_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    name?: string;
                    author?: string;
                    twitter_url?: string;
                    pixiv_url?: string;
                    web_url?: string;
                };
            };
        };
        responses: {
            /** @description 社团已保存 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    delete_admin_circles_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 社团已删除 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                    };
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    get_admin_venues: {
        parameters: {
            query?: {
                page?: string;
                pageSize?: string;
                keyword?: string;
                city?: string;
                capacity_min?: string;
                capacity_max?: string;
                has_parking?: string;
                has_wifi?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 场馆列表 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaginatedResult"] & {
                        /** @example 120 */
                        total?: number;
                        /** @example 1 */
                        page?: number;
                        /** @example 20 */
                        pageSize?: number;
                        items?: {
                            /** @example tokyo-big-sight */
                            id: string;
                            /** @example Tokyo Big Sight */
                            name_en: string;
                            /** @example 東京ビッグサイト */
                            name_ja: string;
                            /** @example 东京 Big Sight */
                            name_zh: string;
                            address_en?: string | null;
                            address_ja?: string | null;
                            address_zh?: string | null;
                            /** @example 35.6298 */
                            lat: number;
                            /** @example 139.793 */
                            lng: number;
                            capacity?: number | null;
                            website_url?: string | null;
                            phone?: string | null;
                            description_en?: string | null;
                            description_ja?: string | null;
                            description_zh?: string | null;
                            facilities?: string | null;
                            transportation?: string | null;
                            parking_info?: string | null;
                            created_at?: string;
                            updated_at?: string;
                        }[];
                    };
                };
            };
        };
    };
    post_admin_venues: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @example Tokyo Big Sight */
                    name_en: string;
                    /** @example 東京ビッグサイト */
                    name_ja: string;
                    /** @example 东京 Big Sight */
                    name_zh: string;
                    address_en?: string | null;
                    address_ja?: string | null;
                    address_zh?: string | null;
                    /** @example 35.6298 */
                    lat: number;
                    /** @example 139.793 */
                    lng: number;
                    capacity?: number | null;
                    website_url?: string | null;
                    phone?: string | null;
                    description_en?: string | null;
                    description_ja?: string | null;
                    description_zh?: string | null;
                    facilities?: string | null;
                    transportation?: string | null;
                    parking_info?: string | null;
                };
            };
        };
        responses: {
            /** @description 创建成功 */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example tokyo-big-sight */
                        id: string;
                        /** @example Tokyo Big Sight */
                        name_en: string;
                        /** @example 東京ビッグサイト */
                        name_ja: string;
                        /** @example 东京 Big Sight */
                        name_zh: string;
                        address_en?: string | null;
                        address_ja?: string | null;
                        address_zh?: string | null;
                        /** @example 35.6298 */
                        lat: number;
                        /** @example 139.793 */
                        lng: number;
                        capacity?: number | null;
                        website_url?: string | null;
                        phone?: string | null;
                        description_en?: string | null;
                        description_ja?: string | null;
                        description_zh?: string | null;
                        facilities?: string | null;
                        transportation?: string | null;
                        parking_info?: string | null;
                        created_at?: string;
                        updated_at?: string;
                    };
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    get_admin_venues_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 场馆详情 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example tokyo-big-sight */
                        id: string;
                        /** @example Tokyo Big Sight */
                        name_en: string;
                        /** @example 東京ビッグサイト */
                        name_ja: string;
                        /** @example 东京 Big Sight */
                        name_zh: string;
                        address_en?: string | null;
                        address_ja?: string | null;
                        address_zh?: string | null;
                        /** @example 35.6298 */
                        lat: number;
                        /** @example 139.793 */
                        lng: number;
                        capacity?: number | null;
                        website_url?: string | null;
                        phone?: string | null;
                        description_en?: string | null;
                        description_ja?: string | null;
                        description_zh?: string | null;
                        facilities?: string | null;
                        transportation?: string | null;
                        parking_info?: string | null;
                        created_at?: string;
                        updated_at?: string;
                    };
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    put_admin_venues_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @example Tokyo Big Sight */
                    name_en?: string;
                    /** @example 東京ビッグサイト */
                    name_ja?: string;
                    /** @example 东京 Big Sight */
                    name_zh?: string;
                    address_en?: string | null;
                    address_ja?: string | null;
                    address_zh?: string | null;
                    /** @example 35.6298 */
                    lat?: number;
                    /** @example 139.793 */
                    lng?: number;
                    capacity?: number | null;
                    website_url?: string | null;
                    phone?: string | null;
                    description_en?: string | null;
                    description_ja?: string | null;
                    description_zh?: string | null;
                    facilities?: string | null;
                    transportation?: string | null;
                    parking_info?: string | null;
                };
            };
        };
        responses: {
            /** @description 更新成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    delete_admin_venues_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 删除成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"];
                };
            };
            /** @description 场馆正在使用中，无法删除 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    post_admin_images_upload: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "multipart/form-data": {
                    /**
                     * Format: binary
                     * @description 图片文件
                     */
                    file?: string;
                    /**
                     * @description 图片分类
                     * @example event
                     * @enum {string}
                     */
                    category: "event" | "circle" | "venue";
                    /**
                     * @description 关联的资源ID
                     * @example resource-uuid-789
                     */
                    resourceId: string;
                    /**
                     * @description 图片类型
                     * @example poster
                     * @enum {string}
                     */
                    imageType: "poster" | "logo" | "banner" | "gallery";
                    /**
                     * @description 图片变体
                     * @example thumb
                     * @enum {string}
                     */
                    variant: "original" | "large" | "medium" | "thumb";
                    /**
                     * @description 关联同一组图片的标识，可选
                     * @example group-uuid-456
                     */
                    groupId?: string;
                };
            };
        };
        responses: {
            /** @description 图片上传成功 */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    delete_admin_images: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /**
                     * @description 要删除的图片相对路径列表
                     * @example [
                     *       "/images/events/reitaisai-22/poster_thumb.jpg"
                     *     ]
                     */
                    relativePaths: string[];
                };
            };
        };
        responses: {
            /** @description 图片删除完成 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    get_admin_images_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 图片信息 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"];
                };
            };
            /** @description 图片不存在 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    get_admin_users: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 用户列表 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaginatedResult"] & {
                        /** @example 120 */
                        total?: number;
                        /** @example 1 */
                        page?: number;
                        /** @example 20 */
                        pageSize?: number;
                        items?: {
                            /** @example uuid-123 */
                            id: string;
                            /** @example alice */
                            username: string;
                            /** @example viewer */
                            role: string;
                        }[];
                    };
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    post_admin_users: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @example alice */
                    username: string;
                    /** @example pwd12345 */
                    password: string;
                    /** @example viewer */
                    role?: string;
                };
            };
        };
        responses: {
            /** @description 用户创建成功 */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_admin_users_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 用户详情 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example uuid-123 */
                        id: string;
                        /** @example alice */
                        username: string;
                        /** @example viewer */
                        role: string;
                    };
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    put_admin_users_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    /** @example alice */
                    username?: string;
                    /** @example editor */
                    role?: string;
                    /** @example newpassword */
                    password?: string;
                };
            };
        };
        responses: {
            /** @description 用户信息已更新 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SuccessResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    delete_admin_users_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 用户已删除 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                    };
                };
            };
        };
    };
    get_admin_logs: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 日志列表 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_admin_stats: {
        parameters: {
            query?: {
                year?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 统计数据 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        totals: {
                            /** @example 123 */
                            circles: number;
                            /** @example 456 */
                            artists: number;
                            /** @example 78 */
                            events: number;
                        };
                        /** @example 2025 */
                        year: number;
                        eventsByMonth: {
                            /** @example 01 */
                            month: string;
                            /** @example 12 */
                            count: number;
                        }[];
                    };
                };
            };
        };
    };
}
