import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { useDeleteAdminEventsId } from "@/api/generated/ayafeedComponents";
import { queryKeys } from "@/constants/queryKeys";
import type { AdminEvent } from "./useAdminEvents";

export function useDeleteEvent() {
  const qc = useQueryClient();

  // 使用生成的 API hook
  const deleteEventMutation = useDeleteAdminEventsId({
    onMutate: async (variables) => {
      const eventId = variables.pathParams.id;

      // 1. 取消正在进行的 adminEvents 查询，避免竞态
      await qc.cancelQueries({ queryKey: queryKeys.adminEvents() });

      // 2. 获取所有 adminEvents 查询的旧数据
      const prevQueries = qc.getQueriesData({
        queryKey: queryKeys.adminEvents(),
      });

      // 3. 立即从列表中移除要删除的事件（乐观更新）
      prevQueries.forEach(([key, oldData]) => {
        if (oldData && typeof oldData === 'object' && 'items' in oldData) {
          const typedOldData = oldData as { items: AdminEvent[]; total: number; [key: string]: any };
          qc.setQueryData(key, {
            ...typedOldData,
            items: typedOldData.items.filter((event: AdminEvent) => event.id !== eventId),
            total: Math.max(0, typedOldData.total - 1),
          });
        }
      });

      // 把旧数据作为 context 用于错误回滚
      return { prevQueries };
    },

    onError: (error, variables, context) => {
      // 请求失败时回滚乐观更新
      context?.prevQueries?.forEach(([key, data]) => {
        qc.setQueryData(key, data);
      });

      // 显示错误提示
      toast.error('删除失败，请重试');
      console.error('删除失败:', error);
    },

    onSuccess: () => {
      // 显示成功提示
      toast.success('展会删除成功');
    },

    onSettled: () => {
      // 最终刷新查询确保数据一致性
      qc.invalidateQueries({ queryKey: queryKeys.adminEvents() });
      qc.invalidateQueries({ predicate: ({ queryKey }) => Array.isArray(queryKey) && queryKey[0] === "events" });
    },
  });

  return {
    ...deleteEventMutation,
    mutate: (id: string, options?: any) => {
      deleteEventMutation.mutate(
        { pathParams: { id } },
        {
          ...options,
          onSuccess: (data, variables, context) => {
            options?.onSuccess?.(data, variables, context);
          },
          onError: (error, variables, context) => {
            options?.onError?.(error, variables, context);
          }
        }
      );
    }
  };
}