"use client";

import React from "react";
import { UseFormReturn } from "react-hook-form";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import VenueSelector from "./VenueSelector";
import { EventImageUpload } from "@/components/events/EventImageUpload";
import { MultilingualContentManager } from "@/components/rich-text-editor";
import { RichTextTabsManager } from "@/components/rich-text-tabs";
import type { MultilingualEventInput } from "@/schemas/event";
import { dateSortToDateString, dateStringToDateSort } from "@/utils/dateSort";

// 错误消息组件 - 简化重复代码
const ErrorMessage = ({ error }: { error?: { message?: string } }) => {
  if (!error) return null
  return (
    <p className="text-destructive text-xs mt-1">
      {String(error.message || "输入错误")}
    </p>
  )
}

interface MultilingualEventFormProps {
  form: UseFormReturn<any>; // 使用 any 来避免复杂的类型匹配问题
  onSubmit: (values: MultilingualEventInput) => void;
  isSubmitting?: boolean;
  submitText?: string;
  title?: string;
  description?: string;
  eventId?: string; // 添加事件 ID，用于图片上传
}

export default function MultilingualEventForm({
  form,
  onSubmit,
  isSubmitting = false,
  submitText = "保存",
  title = "事件信息",
  description = "请填写三种语言的事件信息",
  eventId
}: MultilingualEventFormProps) {
  const { register, handleSubmit, formState: { errors }, watch, setValue } = form;

  // 监听 date_sort 变化
  const dateSort = watch("date_sort");

  // 处理日期变化
  const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const dateString = event.target.value;
    const newDateSort = dateStringToDateSort(dateString);
    setValue("date_sort", newDateSort, { shouldDirty: true });
  };

  return (
    <Card className="w-full max-w-4xl admin-form">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* ID 字段（可选） */}
          <div>
            <Label htmlFor="event-id">
              事件 ID <span className="text-xs text-muted-foreground">(可选，留空自动生成)</span>
            </Label>
            <Input
              id="event-id"
              {...register("id")}
              placeholder="例：reitaisai-23（留空则自动生成 UUID）"
            />
            <p className="text-xs text-muted-foreground mt-1">
              可以指定自定义 ID，如果留空系统会自动生成唯一的 UUID
            </p>
          </div>

          {/* 事件日期设置 */}
          <div className="border rounded-lg p-4 bg-stone-50 dark:bg-stone-900/50">
            <div className="mb-3">
              <h3 className="text-lg font-semibold text-stone-900 dark:text-stone-100">事件日期设置</h3>
              <p className="text-sm text-stone-600 dark:text-stone-400">
                设置事件的实际举办日期，系统将自动生成排序字段
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="event-date">事件日期 *</Label>
                <Input
                  id="event-date"
                  type="date"
                  value={dateSortToDateString(dateSort)}
                  onChange={handleDateChange}
                  className="w-full"
                />
                <ErrorMessage error={errors.date_sort} />
                <p className="text-xs text-slate-500 mt-1">
                  选择事件的实际举办日期
                </p>
              </div>

              <div>
                <Label htmlFor="date-sort-display">排序字段 (自动生成)</Label>
                <Input
                  id="date-sort-display"
                  value={dateSort || ""}
                  disabled
                  className="bg-slate-100 dark:bg-slate-800"
                />
                <p className="text-xs text-slate-500 mt-1">
                  格式：YYYYMMDD，用于事件排序
                </p>
              </div>
            </div>
          </div>

          {/* 多语言标签页 */}
          <Tabs defaultValue="zh" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-slate-100 dark:bg-slate-800">
              <TabsTrigger
                value="zh"
                className="data-[state=active]:bg-slate-900 data-[state=active]:text-white hover:bg-slate-200 dark:data-[state=active]:bg-slate-100 dark:data-[state=active]:text-slate-900 dark:hover:bg-slate-700"
              >
                中文
              </TabsTrigger>
              <TabsTrigger
                value="ja"
                className="data-[state=active]:bg-slate-900 data-[state=active]:text-white hover:bg-slate-200 dark:data-[state=active]:bg-slate-100 dark:data-[state=active]:text-slate-900 dark:hover:bg-slate-700"
              >
                日本語
              </TabsTrigger>
              <TabsTrigger
                value="en"
                className="data-[state=active]:bg-slate-900 data-[state=active]:text-white hover:bg-slate-200 dark:data-[state=active]:bg-slate-100 dark:data-[state=active]:text-slate-900 dark:hover:bg-slate-700"
              >
                English
              </TabsTrigger>
            </TabsList>

            {/* 中文标签页 */}
            <TabsContent value="zh" className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label htmlFor="name-zh">中文名称 *</Label>
                  <Input
                    id="name-zh"
                    {...register("name_zh")}
                    placeholder="例：第二十二回博丽神社例大祭"
                  />
                  <ErrorMessage error={errors.name_zh} />
                </div>
                <div>
                  <Label htmlFor="date-zh">中文日期 *</Label>
                  <Input
                    id="date-zh"
                    {...register("date_zh")}
                    placeholder="例：2025年5月3日(周六) 10:30 – 15:30"
                  />
                  <ErrorMessage error={errors.date_zh} />
                </div>

              </div>
            </TabsContent>

            {/* 日文标签页 */}
            <TabsContent value="ja" className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label htmlFor="name-ja">日文名称 *</Label>
                  <Input
                    id="name-ja"
                    {...register("name_ja")}
                    placeholder="例：第二十二回博麗神社例大祭"
                  />
                  <ErrorMessage error={errors.name_ja} />
                </div>
                <div>
                  <Label htmlFor="date-ja">日文日期 *</Label>
                  <Input
                    id="date-ja"
                    {...register("date_ja")}
                    placeholder="例：2025年5月3日(土・祝) 10:30 – 15:30"
                  />
                  <ErrorMessage error={errors.date_ja} />
                </div>

              </div>
            </TabsContent>

            {/* 英文标签页 */}
            <TabsContent value="en" className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label htmlFor="name-en">英文名称 *</Label>
                  <Input
                    id="name-en"
                    {...register("name_en")}
                    placeholder="例：Reitaisai 22"
                  />
                  <ErrorMessage error={errors.name_en} />
                </div>
                <div>
                  <Label htmlFor="date-en">英文日期 *</Label>
                  <Input
                    id="date-en"
                    {...register("date_en")}
                    placeholder="例：May 3, 2025 (Sat) 10:30 – 15:30"
                  />
                  <ErrorMessage error={errors.date_en} />
                </div>

              </div>
            </TabsContent>
          </Tabs>

          {/* 通用字段 */}
          <div className="space-y-4 border-t pt-4">
            <h3 className="text-lg font-medium">通用信息</h3>

            <div>
              <Label htmlFor="venue-selector">展会场馆 *</Label>
              <VenueSelector
                value={watch("venue_id")}
                onValueChange={(venueId) => form.setValue("venue_id", venueId)}
                placeholder="选择展会场馆..."
              />
              <ErrorMessage error={errors.venue_id} />
            </div>

            <div>
              <Label>封面图片</Label>
              {eventId ? (
                <EventImageUpload
                  eventId={eventId}
                  onUploadSuccess={(images) => {
                    // 上传成功后，更新表单中的 image_url 字段
                    if (images.length > 0) {
                      // 使用 medium 变体的路径作为默认显示
                      const mediumImage = images.find(img => img.relativePath.includes('medium')) || images[0];
                      form.setValue("image_url", mediumImage.relativePath);
                    }
                  }}
                  onUploadError={(error) => {
                    console.error('图片上传失败:', error);
                  }}
                />
              ) : (
                <div className="text-sm text-muted-foreground p-4 border border-dashed rounded-lg">
                  保存事件后可上传图片
                </div>
              )}

              {/* 保留隐藏的 image_url 字段用于表单提交 */}
              <input
                type="hidden"
                {...register("image_url")}
              />

              {/* 显示当前图片路径（如果有） */}
              {watch("image_url") && (
                <p className="text-xs text-muted-foreground mt-2">
                  当前图片: {watch("image_url")}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="url">官网 URL</Label>
              <Input
                id="url"
                type="url"
                {...register("url")}
                placeholder="https://example.com"
              />
              <ErrorMessage error={errors.url} />
            </div>
          </div>

          <Button type="submit" disabled={isSubmitting} className="w-full">
            {isSubmitting ? "保存中..." : submitText}
          </Button>
        </form>

        {/* 多语言富文本内容管理 - 移出表单外部 */}
        {eventId && (
          <div className="border-t pt-4 mt-4">
            <MultilingualContentManager
              entityType="event"
              entityId={eventId}
            />
          </div>
        )}

        {/* 新版本富文本标签页管理 */}
        {eventId && (
          <div className="border-t pt-4 mt-4">
            <RichTextTabsManager
              entityType="event"
              entityId={eventId}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
