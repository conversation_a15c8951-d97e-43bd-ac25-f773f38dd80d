import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useLocale } from "next-intl";

import {
  usePostAdminEvents,
  type PostAdminEventsRequestBody
} from "@/api/generated/ayafeedComponents";
import { queryKeys } from "@/constants/queryKeys";
import type { MultilingualEventInput } from "@/schemas/event";
import type { AdminEvent } from "./useAdminEvents";
import { getLocalizedField } from "@/app/events/[id]/utils";

/**
 * 将表单数据转换为 API 请求格式
 * 支持自定义 ID：如果提供了 id 则使用，否则后端会自动生成 UUID
 */
function transformEventInputToApiFormat(input: MultilingualEventInput): PostAdminEventsRequestBody {
  const payload: PostAdminEventsRequestBody = {
    name_en: input.name_en,
    name_ja: input.name_ja,
    name_zh: input.name_zh,
    date_en: input.date_en,
    date_ja: input.date_ja,
    date_zh: input.date_zh,
    date_sort: input.date_sort,
    image_url: input.image_url || null,
    venue_id: input.venue_id,
    url: input.url || null,
  };

  // 如果提供了自定义 ID，则包含在请求中
  if (input.id && input.id.trim()) {
    payload.id = input.id.trim();
  }

  return payload;
}

/**
 * 将表单数据转换为乐观更新用的 AdminEvent 格式
 */
function transformInputToAdminEvent(input: MultilingualEventInput, locale: string): AdminEvent {
  return {
    id: input.id || `temp-${Date.now()}`, // 临时ID，服务器会返回真实ID
    name: getLocalizedField({
      zh: input.name_zh,
      ja: input.name_ja,
      en: input.name_en
    }, locale) || input.name_en || '',
    date: getLocalizedField({
      zh: input.date_zh,
      ja: input.date_ja,
      en: input.date_en
    }, locale) || input.date_en || '',
    venue_name: `Venue ID: ${input.venue_id}`, // 临时显示，实际需要通过venue API获取
    venue_address: undefined,
    url: input.url || undefined,
    image_url: input.image_url || undefined,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
}

export function useCreateEvent() {
  const qc = useQueryClient();
  const locale = useLocale();

  // 使用生成的 API hook
  const createEventMutation = usePostAdminEvents({
    onMutate: async (variables) => {
      // 从 body 中重建输入数据用于乐观更新
      const apiData = variables.body;
      const input: MultilingualEventInput = {
        id: apiData.id,
        name_en: apiData.name_en,
        name_ja: apiData.name_ja,
        name_zh: apiData.name_zh,
        date_en: apiData.date_en,
        date_ja: apiData.date_ja,
        date_zh: apiData.date_zh,
        date_sort: apiData.date_sort,
        image_url: apiData.image_url || undefined,
        venue_id: apiData.venue_id,
        url: apiData.url || undefined,
      };

      // 1. 取消正在进行的 adminEvents 查询，避免竞态
      await qc.cancelQueries({ queryKey: queryKeys.adminEvents() });

      // 2. 获取所有 adminEvents 查询的旧数据
      const prevQueries = qc.getQueriesData({
        queryKey: queryKeys.adminEvents(),
      });

      // 3. 创建乐观更新的事件数据
      const optimisticEvent = transformInputToAdminEvent(input, locale);

      // 4. 立即添加到列表顶部（乐观更新）
      prevQueries.forEach(([key, oldData]) => {
        if (oldData && typeof oldData === 'object' && 'items' in oldData) {
          const typedOldData = oldData as { items: AdminEvent[]; total: number; [key: string]: any };
          qc.setQueryData(key, {
            ...typedOldData,
            items: [optimisticEvent, ...typedOldData.items],
            total: typedOldData.total + 1,
          });
        }
      });

      // 把旧数据作为 context 用于错误回滚
      return { prevQueries };
    },

    onError: (error, variables, context) => {
      // 请求失败时回滚乐观更新
      context?.prevQueries?.forEach(([key, data]) => {
        qc.setQueryData(key, data);
      });

      // 显示错误提示
      toast.error('创建失败，请重试');
      console.error('创建失败:', error);
    },

    onSuccess: () => {
      // 显示成功提示
      toast.success('展会创建成功');
    },

    onSettled: () => {
      // 最终刷新查询确保数据一致性
      qc.invalidateQueries({ queryKey: queryKeys.adminEvents() });
      // 刷新前台 /events 列表缓存
      qc.invalidateQueries({ predicate: ({ queryKey }) => Array.isArray(queryKey) && queryKey[0] === "events" });
    },
  });

  return {
    ...createEventMutation,
    mutate: (input: MultilingualEventInput, options?: any) => {
      const apiPayload = transformEventInputToApiFormat(input);

      createEventMutation.mutate(
        { body: apiPayload },
        {
          ...options,
          onSuccess: (data, variables, context) => {
            options?.onSuccess?.(data, variables, context);
          },
          onError: (error, variables, context) => {
            options?.onError?.(error, variables, context);
          }
        }
      );
    }
  };
}