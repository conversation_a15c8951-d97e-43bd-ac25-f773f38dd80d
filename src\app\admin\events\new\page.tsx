"use client";

import { useRouter } from "next/navigation";

import MultilingualEventForm from "@/components/admin/MultilingualEventForm";
import { useZodForm } from "@/hooks";
import { useCreateEvent } from "@/hooks/admin/useCreateEvent";
import { showApiError } from "@/lib/show-error";
import { MultilingualEventInputSchema, type MultilingualEventInput } from "@/schemas/event";
import { generateDateSort } from "@/utils/dateSort";

// 展会 ID 支持自定义：用户可以指定 ID，也可以留空让后端自动生成 UUID

export default function NewEventPage() {
  const router = useRouter();

  // 使用 zodResolver 进行表单校验
  const form = useZodForm(MultilingualEventInputSchema, {
    defaultValues: {
      id: "", // 可选字段，用户可以指定自定义 ID
      name_en: "",
      name_ja: "",
      name_zh: "",
      date_en: "",
      date_ja: "",
      date_zh: "",
      date_sort: 0, // 将在表单中设置实际值
      image_url: "",
      venue_id: "",
      url: "",
    } satisfies Partial<MultilingualEventInput>,
    mode: "onBlur",
  });

  const createEvent = useCreateEvent();

  async function onSubmit(values: MultilingualEventInput) {
    // 验证 date_sort 是否已设置
    if (!values.date_sort || values.date_sort === 0) {
      form.setError("date_sort", {
        type: "manual",
        message: "请选择事件日期"
      });
      return;
    }

    // 传递完整的表单数据，包括可选的 id 字段
    // 如果 id 为空，后端会自动生成 UUID
    const payload = {
      ...values,
      date_sort: values.date_sort,
    };

    createEvent.mutate(payload, {
      onSuccess: () => {
        form.reset();
        router.push("/admin/events");
      },
      onError: (err: unknown) => { showApiError(err); },
    });
  }



  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">新增展会</h1>
        <p className="text-muted-foreground mt-2">
          请填写三种语言的展会信息，确保国际化支持
        </p>
      </div>

      <MultilingualEventForm
        form={form}
        onSubmit={onSubmit}
        isSubmitting={createEvent.isPending}
        submitText="创建展会"
        title="新建展会信息"
        description="请完整填写中文、日文、英文三种语言的展会信息"
      />
    </div>
  );
}