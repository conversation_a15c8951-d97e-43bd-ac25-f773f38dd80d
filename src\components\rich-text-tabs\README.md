# Rich Text Tabs 组件

新版本的富文本标签页管理系统，支持动态配置和多语言内容管理。

## ⚠️ 重要修复说明

在实现过程中发现并修复了以下关键问题：

### API 结构修正
1. **配置接口返回数组**: `GetRichtexttabsConfigsEntityTypeLanguageCodeResponse` 是配置对象数组，不是单个对象
2. **字段名修正**: API 使用 `sort_order` 而非 `order_index`，使用 `placeholder` 而非 `description`
3. **React Query 版本兼容**: 移除了已废弃的 `onSuccess` 选项，改用 `useEffect`
4. **错误处理修正**: 错误对象结构为 `{status, payload}` 而非 `{message}`

### 数据结构对齐
- 标签页配置包含完整的实体和语言信息
- 内容保存使用正确的 `content_type` 字段
- 批量保存使用 `contents` 对象结构

## 功能特性

### 🆕 新版本优势

- **动态标签页配置**: 不再限制于固定的内容类型，支持自定义标签页
- **多语言独立管理**: 每种语言可以有不同的标签页配置
- **预设保护机制**: 核心标签页受保护，不能被意外删除
- **批量操作支持**: 支持批量保存和状态管理
- **更好的性能**: 优化的缓存策略和数据加载

### 📊 与旧版本对比

| 功能 | 旧版本 (MultilingualContentManager) | 新版本 (RichTextTabsManager) |
|------|-------------------------------------|------------------------------|
| 标签页类型 | 固定 4 种 (introduction, highlights, guide, notices) | 动态配置，可自定义 |
| 多语言支持 | 基础支持 | 完整的独立语言管理 |
| 配置管理 | 无 | 完整的配置 CRUD 操作 |
| 批量操作 | 有限支持 | 完整的批量操作 |
| 预设保护 | 无 | 支持预设标签页保护 |

## 组件使用

### RichTextTabsManager

用于管理页面的富文本编辑器，支持完整的编辑功能。

```tsx
import { RichTextTabsManager } from '@/components/rich-text-tabs';

function AdminEventEdit({ eventId }: { eventId: string }) {
  return (
    <RichTextTabsManager
      entityType="event"
      entityId={eventId}
      defaultLanguage="zh"
    />
  );
}
```

**Props:**
- `entityType`: 实体类型 ('event' | 'venue')
- `entityId`: 实体 ID
- `className?`: 自定义样式类名
- `defaultLanguage?`: 默认语言 ('zh' | 'ja' | 'en')

### RichTextTabsViewer

用于前台页面的只读内容显示。

```tsx
import { RichTextTabsViewer } from '@/components/rich-text-tabs';

function EventOverview({ eventId }: { eventId: string }) {
  return (
    <RichTextTabsViewer
      entityType="event"
      entityId={eventId}
      showLanguageSwitch={true}
    />
  );
}
```

**Props:**
- `entityType`: 实体类型 ('event' | 'venue')
- `entityId`: 实体 ID
- `className?`: 自定义样式类名
- `showLanguageSwitch?`: 是否显示语言切换器
- `defaultLanguage?`: 默认语言

## Hook 使用

### useRichTextTabs

核心数据管理 Hook，提供完整的状态管理和操作方法。

```tsx
import { useRichTextTabs } from '@/hooks/useRichTextTabs';

function MyComponent() {
  const {
    configs,           // 标签页配置列表
    tabs,             // 标签页内容数据
    isLoading,        // 加载状态
    activeTabKey,     // 当前活跃标签页
    getTabContent,    // 获取标签页内容
    updateTabContent, // 更新标签页内容
    saveTab,          // 保存单个标签页
    saveAllTabs,      // 批量保存
    hasUnsavedChanges, // 是否有未保存更改
  } = useRichTextTabs({
    entityType: 'event',
    entityId: 'event-123',
    languageCode: 'zh',
  });

  return (
    // 你的组件 JSX
  );
}
```

## API 接口

新版本使用以下 API 接口：

### 配置管理
- `GET /rich-text-tabs/configs/{entityType}/{languageCode}` - 获取实体类型的活跃配置
- `GET /rich-text-tabs/configs/{entityType}/{entityId}/{languageCode}/all` - 获取实体实例的所有配置（管理模式）
- `POST /rich-text-tabs/configs` - 创建新配置（需要 entity_id 实现隔离）
- `PUT /rich-text-tabs/configs/{id}` - 更新配置
- `DELETE /rich-text-tabs/configs/{id}` - 删除配置

### 内容管理
- `GET /rich-text-tabs/tabs/{entityType}/{entityId}/{languageCode}` - 获取完整数据
- `POST /rich-text-tabs/content` - 创建/更新内容
- `POST /rich-text-tabs/content/batch` - 批量操作

## 数据结构

### TabConfig
```typescript
interface TabConfig {
  id: string;
  key: string;           // 标签页键名
  label: string;         // 显示标签
  description?: string;  // 描述信息
  is_active: boolean;    // 是否活跃
  is_preset: boolean;    // 是否为预设
  order_index: number;   // 排序索引
  created_at: string;
  updated_at: string;
}
```

### TabContent
```typescript
interface TabContent {
  config: TabConfig;
  content?: string;      // HTML 内容
}
```

## 迁移指南

### 从旧版本迁移

1. **保持兼容**: 新旧版本可以并存，不需要立即迁移
2. **逐步替换**: 建议在新功能中使用新版本
3. **数据兼容**: 新版本可以读取旧版本的数据

### 迁移步骤

1. 在需要的页面导入新组件
2. 替换旧组件为新组件
3. 调整相关的类型定义
4. 测试功能完整性

## 注意事项

1. **语言代码**: 新版本需要明确指定语言代码
2. **权限控制**: 预设标签页受保护，不能删除
3. **缓存策略**: 配置数据缓存 5 分钟，内容数据缓存 2 分钟
4. **错误处理**: 组件内置了完整的错误处理和加载状态

## 开发建议

1. **性能优化**: 使用 React.memo 包装组件以避免不必要的重渲染
2. **错误边界**: 在上层组件添加错误边界来捕获异常
3. **测试覆盖**: 为关键功能编写单元测试和集成测试
4. **用户体验**: 添加适当的加载状态和错误提示

## 故障排除

### 常见问题

1. **配置加载失败**: 检查 entityType 和 languageCode 是否正确
2. **内容保存失败**: 确认用户权限和网络连接
3. **语言切换无效**: 检查组件的 key 属性是否正确设置

### 调试技巧

1. 使用浏览器开发者工具查看网络请求
2. 检查 React Query DevTools 中的缓存状态
3. 查看控制台错误信息和警告
